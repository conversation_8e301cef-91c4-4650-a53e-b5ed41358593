{"name": "flex1", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "./scripts/dev-server.sh", "dev:simple": "vite dev --port 5173", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "migrate": "./scripts/migrate.sh up", "migrate:status": "./scripts/migrate.sh status", "hasura:setup": "./scripts/setup-hasura.sh", "codegen": "graphql-codegen --config codegen.ts", "codegen:watch": "graphql-codegen --config codegen.ts --watch", "test:unit": "vitest", "test": "npm run test:unit -- --run && npm run test:e2e", "test:e2e": "playwright test", "deploy:dev": "./scripts/deployment/deploy-dev.sh", "deploy:staging": "./scripts/deployment/deploy-staging.sh", "deploy:prod": "./scripts/deployment/deploy-prod.sh", "wrangler:dev": "wrangler dev .svelte-kit/cloudflare", "wrangler:tail:dev": "wrangler tail --env development", "wrangler:tail:prod": "wrangler tail --env production"}, "devDependencies": {"@21st-extension/toolbar": "^0.5.14", "@cloudflare/workers-types": "^4.20250726.0", "@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/typed-document-node": "^5.1.2", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.515.0", "@playwright/test": "^1.49.1", "@sveltejs/adapter-cloudflare": "^7.1.1", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/svelte": "^5.2.8", "@vitest/browser": "^3.2.3", "autoprefixer": "^10.4.21", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "graphql": "^16.11.0", "playwright": "^1.53.0", "postcss": "^8.5.6", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^7.0.4", "vitest": "^3.2.3", "vitest-browser-svelte": "^0.1.0", "wrangler": "^4.26.0"}, "dependencies": {"@graphql-typed-document-node/core": "^3.2.0", "@logto/node": "^3.1.6", "@logto/sveltekit": "^0.3.19", "@tailwindcss/postcss": "^4.1.11", "@types/uuid": "^10.0.0", "@urql/exchange-auth": "^2.2.1", "@urql/svelte": "^4.2.3", "bits-ui": "^2.8.13", "clsx": "^2.1.1", "svelte-lucide": "^2.0.1", "svelte-motion": "^0.12.2", "sveltekit-superforms": "^2.27.1", "tailwind-merge": "^3.3.1", "tailwind-variants": "^2.0.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.76"}}