#!/bin/bash

echo "🚀 Starting Flex1 Local Development Environment..."

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop."
    exit 1
fi

# Stop any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose -f docker-compose.dev.yml down

# Start the services
echo "📦 Starting Docker services..."
docker-compose -f docker-compose.dev.yml up -d

echo "⏳ Waiting for PostgreSQL to be ready..."
while ! docker exec flex1-postgres pg_isready -U postgres > /dev/null 2>&1; do
    echo "   Still waiting for PostgreSQL..."
    sleep 2
done

echo "🗄️ Creating databases..."
docker exec flex1-postgres psql -U postgres -c "CREATE DATABASE logto;" 2>/dev/null || echo "   Logto database already exists"

echo "⏳ Waiting for <PERSON><PERSON> to be ready..."
sleep 5

# Check if <PERSON><PERSON> is responding
max_attempts=30
attempt=0
while [ $attempt -lt $max_attempts ]; do
    if curl -s http://localhost:8080/healthz > /dev/null; then
        break
    fi
    echo "   Still waiting for <PERSON><PERSON>... (attempt $((attempt + 1))/$max_attempts)"
    sleep 2
    attempt=$((attempt + 1))
done

echo "⏳ Waiting for Logto to be ready..."
sleep 5

# Check if Logto is responding
max_attempts=30
attempt=0
while [ $attempt -lt $max_attempts ]; do
    if curl -s http://localhost:3003 > /dev/null; then
        break
    fi
    echo "   Still waiting for Logto... (attempt $((attempt + 1))/$max_attempts)"
    sleep 2
    attempt=$((attempt + 1))
done

echo ""
echo "✅ Local development environment is ready!"
echo ""
echo "🎉 Available Services:"
echo "   🐘 PostgreSQL:     localhost:5432 (user: postgres, pass: postgres)"
echo "   🚀 Hasura Console: http://localhost:8080 (admin secret: admin_secret_key)"
echo "   🔐 Logto Core:     http://localhost:3002"
echo "   ⚙️  Logto Admin:    http://localhost:3003"
echo ""
echo "📝 Next Steps:"
echo "   1. Open Hasura Console: http://localhost:8080"
echo "   2. Configure Logto at: http://localhost:3003"
echo "   3. Create your database schema in Hasura"
echo "   4. Set up authentication flow with Logto"
echo ""
echo "🛑 To stop services: docker-compose -f docker-compose.dev.yml down"
