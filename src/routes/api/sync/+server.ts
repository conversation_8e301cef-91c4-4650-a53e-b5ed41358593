import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { dbService } from '$lib/graphql/database-service';
import { env } from '$env/dynamic/private';

interface LogtoUser {
	id: string;
	username?: string;
	primaryEmail?: string;
	primaryPhone?: string;
	name?: string;
	avatar?: string;
	customData?: Record<string, unknown>;
	identities?: Record<string, unknown>;
	lastSignInAt?: number;
	createdAt: number;
	updatedAt: number;
	profile?: Record<string, unknown>;
}

// Get management token (reuse from register endpoint)
async function getManagementToken(): Promise<string> {
	const response = await fetch(`${env.LOGTO_ENDPOINT}/oidc/token`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded'
		},
		body: new URLSearchParams({
			grant_type: 'client_credentials',
			client_id: env.LOGTO_M2M_APP_ID!,
			client_secret: env.LOGTO_M2M_APP_SECRET!,
			resource: 'https://default.logto.app/api',
			scope: 'all'
		})
	});

	if (!response.ok) {
		throw new Error('Failed to get management token');
	}

	const data = await response.json();
	return data.access_token;
}

export const POST: RequestHandler = async () => {
	try {
		console.log('Syncing users from Logto to database...');

		// Get all users from Logto
		const token = await getManagementToken();
		const response = await fetch(`${env.LOGTO_ENDPOINT}/api/users`, {
			headers: {
				'Authorization': `Bearer ${token}`
			}
		});

		if (!response.ok) {
			throw new Error('Failed to fetch users from Logto');
		}

		const logtoUsers: LogtoUser[] = await response.json();
		console.log(`Found ${logtoUsers.length} users in Logto`);

		// Sync each user to the database
		const syncResults = [];
		for (const user of logtoUsers) {
			try {
				const result = await dbService.syncUser({
					id: user.id,
					username: user.username,
					primaryEmail: user.primaryEmail,
					primaryPhone: user.primaryPhone,
					name: user.name,
					avatar: user.avatar,
					customData: user.customData,
					identities: user.identities,
					lastSignInAt: user.lastSignInAt,
					createdAt: user.createdAt,
					updatedAt: user.updatedAt,
					profile: user.profile
				});

				syncResults.push({
					id: user.id,
					email: user.primaryEmail,
					success: true,
					dbId: result?.upsert_users_one?.id
				});

				console.log(`Synced user: ${user.primaryEmail} -> DB ID: ${result?.upsert_users_one?.id}`);
			} catch (err) {
				console.error(`Failed to sync user ${user.id}:`, err);
				syncResults.push({
					id: user.id,
					email: user.primaryEmail,
					success: false,
					error: err instanceof Error ? err.message : 'Unknown error'
				});
			}
		}

		return json({
			success: true,
			message: `Synced ${syncResults.filter(r => r.success).length} of ${logtoUsers.length} users`,
			results: syncResults
		});

	} catch (err) {
		console.error('Sync error:', err);
		return json({
			success: false,
			message: err instanceof Error ? err.message : 'Sync failed'
		}, { status: 500 });
	}
};
