import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { dbService } from '$lib/graphql/database-service';
import { v4 as uuidv4 } from 'uuid';

export const POST: RequestHandler = async ({ request }) => {
	try {
		console.log('Testing Logto webhook simulation...');
		
		// Simulate a Logto PostRegister webhook payload
		const simulatedPayload = {
			hookId: uuidv4(),
			event: 'PostRegister',
			sessionId: uuidv4(),
			userAgent: 'Test Browser',
			ip: '127.0.0.1',
			createdAt: new Date().toISOString(),
			data: {
				id: uuidv4(),
				username: 'newuser' + Date.now(),
				primaryEmail: `test${Date.now()}@example.com`,
				name: 'New Test User',
				avatar: null,
				customData: {},
				identities: {},
				lastSignInAt: Date.now(),
				createdAt: Date.now(),
				updatedAt: Date.now(),
				profile: {},
				applicationId: 'test-app',
				isOnboarded: false
			}
		};

		console.log('Simulating webhook with event:', simulatedPayload.event);
		
		// Log the webhook event
		const logResult = await dbService.logWebhookEvent({
			webhookType: 'logto',
			logtoEventId: simulatedPayload.hookId,
			payload: simulatedPayload,
			status: 'pending'
		});
		
		// Process the user registration
		const userResult = await dbService.syncUser(simulatedPayload.data);
		console.log('User sync completed:', userResult?.insert_users_one?.id);
		
		// Update webhook log to processed
		await dbService.logWebhookEvent({
			webhookType: 'logto',
			logtoEventId: simulatedPayload.hookId,
			payload: simulatedPayload,
			status: 'processed'
		});
		
		return json({
			success: true,
			message: 'Webhook simulation completed successfully',
			results: {
				event: simulatedPayload.event,
				hookId: simulatedPayload.hookId,
				userId: userResult?.insert_users_one?.id,
				userEmail: userResult?.insert_users_one?.email
			}
		});
		
	} catch (err) {
		console.error('Webhook simulation error:', err);
		return json({
			success: false,
			error: err instanceof Error ? err.message : 'Unknown error',
			details: err
		}, { status: 500 });
	}
};
