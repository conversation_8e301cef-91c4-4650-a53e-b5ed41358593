import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { LOGTO_WEBHOOK_SECRET } from '$env/static/private';
import crypto from 'crypto';
import { dbService } from '$lib/graphql/database-service';

// Types for Logto webhook events
interface LogtoWebhookPayload {
	hookId: string;
	event: string;
	data: UserCreatedData | OrganizationMembershipData | Record<string, unknown>;
	sessionId?: string;
	userAgent?: string;
	ip?: string;
	createdAt: string;
}

interface UserCreatedData {
	id: string;
	username?: string;
	primaryEmail?: string;
	primaryPhone?: string;
	name?: string;
	avatar?: string;
	customData?: Record<string, unknown>;
	identities?: Record<string, unknown>;
	lastSignInAt?: number;
	createdAt: number;
	updatedAt: number;
	profile?: Record<string, unknown>;
	applicationId?: string;
	isOnboarded?: boolean;
}

interface OrganizationMembershipData {
	id: string;
	userId: string;
	organizationId: string;
	organizationRoles: string[];
	createdAt: number;
}

// Verify webhook signature
function verifyWebhookSignature(body: string, signature: string, secret: string): boolean {
	try {
		// Logto uses SHA-256 HMAC for webhook signatures
		const expectedSignature = crypto
			.createHmac('sha256', secret)
			.update(body)
			.digest('hex');
		
		// Signature format: sha256=<hash>
		const providedSignature = signature.replace('sha256=', '');
		
		return crypto.timingSafeEqual(
			Buffer.from(expectedSignature, 'hex'),
			Buffer.from(providedSignature, 'hex')
		);
	} catch (err) {
		console.error('Error verifying webhook signature:', err);
		return false;
	}
}

// Sync user data to our database
async function syncUserToDatabase(userData: UserCreatedData) {
	try {
		console.log('Syncing user to database:', {
			id: userData.id,
			email: userData.primaryEmail,
			username: userData.username,
			name: userData.name
		});
		
		const result = await dbService.syncUser(userData);
		
		console.log('User synced successfully:', result?.upsert_users_one?.id);
		
		return { success: true, userId: result?.upsert_users_one?.id };
	} catch (err) {
		console.error('Error syncing user to database:', err);
		throw err;
	}
}

// Sync organization membership
async function syncOrganizationMembership(membershipData: OrganizationMembershipData) {
	try {
		console.log('Syncing organization membership:', {
			userId: membershipData.userId,
			organizationId: membershipData.organizationId,
			roles: membershipData.organizationRoles
		});
		
		// For now, just take the first role if multiple exist
		const primaryRole = membershipData.organizationRoles[0] || 'member';
		
		const result = await dbService.syncUserOrganizationMembership({
			userId: membershipData.userId,
			organizationId: membershipData.organizationId,
			role: primaryRole,
			status: 'active'
		});
		
		console.log('Organization membership synced successfully');
		
		return { success: true };
	} catch (err) {
		console.error('Error syncing organization membership:', err);
		throw err;
	}
}

export const POST: RequestHandler = async ({ request }) => {
	let payload: LogtoWebhookPayload | undefined;
	
	try {
		// Get the raw body for signature verification
		const body = await request.text();
		
		// Get the signature header
		const signature = request.headers.get('logto-signature-sha-256');
		
		if (!signature) {
			console.error('Missing webhook signature header');
			throw error(401, 'Missing signature header');
		}
		
		// Verify the webhook signature
		if (!LOGTO_WEBHOOK_SECRET) {
			console.error('LOGTO_WEBHOOK_SECRET not configured');
			throw error(500, 'Webhook secret not configured');
		}
		
		if (!verifyWebhookSignature(body, signature, LOGTO_WEBHOOK_SECRET)) {
			console.error('Invalid webhook signature');
			throw error(401, 'Invalid signature');
		}
		
		// Parse the webhook payload
		try {
			payload = JSON.parse(body) as LogtoWebhookPayload;
		} catch (err) {
			console.error('Error parsing webhook payload:', err);
			throw error(400, 'Invalid JSON payload');
		}
		
		if (!payload) {
			throw error(400, 'Invalid payload');
		}
		
		console.log('Received Logto webhook:', {
			hookId: payload.hookId,
			event: payload.event,
			sessionId: payload.sessionId,
			createdAt: payload.createdAt
		});
		
		// Log the webhook event
		const logResult = await dbService.logWebhookEvent({
			webhookType: 'logto',
			logtoEventId: payload.hookId || '',
			payload: payload as Record<string, unknown>,
			status: 'pending'
		});
		
		// Handle different webhook events
		switch (payload.event) {
			case 'PostRegister':
			case 'User.Created':
				await syncUserToDatabase(payload.data as UserCreatedData);
				break;
				
			case 'PostSignIn':
				// Update last login time for existing users
				await syncUserToDatabase(payload.data as UserCreatedData);
				break;
				
			case 'Organization.Membership.Updated':
				await syncOrganizationMembership(payload.data as OrganizationMembershipData);
				break;
				
			case 'Organization.Created':
				console.log('Organization created:', payload.data);
				// TODO: Implement organization sync
				break;
				
			default:
				console.log('Unhandled webhook event:', payload.event);
		}
		
		// Update webhook log to processed status
		if (logResult?.insert_webhook_logs_one?.id) {
			await dbService.logWebhookEvent({
				webhookType: 'logto',
				logtoEventId: payload.hookId,
				payload: payload as Record<string, unknown>,
				status: 'processed'
			});
		}
		
		return json({ 
			success: true, 
			message: 'Webhook processed successfully',
			event: payload.event,
			hookId: payload.hookId
		});
		
	} catch (err) {
		console.error('Webhook processing error:', err);
		
		// Log the error if we have the payload
		if (payload) {
			await dbService.logWebhookEvent({
				webhookType: 'logto',
				logtoEventId: payload.hookId,
				payload: payload as Record<string, unknown>,
				status: 'failed',
				errorMessage: err instanceof Error ? err.message : 'Unknown error'
			});
		}
		
		if (err && typeof err === 'object' && 'status' in err) {
			throw err; // Re-throw SvelteKit errors
		}
		
		throw error(500, 'Internal server error');
	}
};
