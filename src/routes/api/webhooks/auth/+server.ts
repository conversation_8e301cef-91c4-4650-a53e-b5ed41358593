import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const body = await request.text();
		const payload = JSON.parse(body);
		
		console.log('✅ Webhook received:', payload.event);
		
		return json({ success: true });
		
	} catch (err) {
		console.error('Webhook error:', err);
		return json({ success: false }, { status: 500 });
	}
};
