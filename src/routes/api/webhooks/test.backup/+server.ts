import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON>and<PERSON> } from './$types';
import { dbService } from '$lib/graphql/database-service';
import { v4 as uuidv4 } from 'uuid';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const testUser = {
			id: uuidv4(), // Generate a proper UUID
			email: '<EMAIL>',
			username: 'testuser',
			name: 'Test User',
			avatar: null,
			primaryEmail: '<EMAIL>',
			lastSignInAt: Date.now(),
			createdAt: Date.now(),
			updatedAt: Date.now()
		};

		console.log('Testing user sync with:', testUser.id);
		
		// Test user sync
		const result = await dbService.syncUser(testUser);
		console.log('User sync result:', result);
		
		// Test webhook logging
		const logResult = await dbService.logWebhookEvent({
			webhookType: 'test',
			logtoEventId: 'test-event-' + Date.now(),
			payload: { test: 'data' },
			status: 'processed'
		});
		console.log('Webhook log result:', logResult);
		
		// Test user retrieval
		const retrievedUser = await dbService.getUserByLogtoId(testUser.id);
		console.log('Retrieved user:', retrievedUser);
		
		return json({
			success: true,
			message: 'Complete webhook test completed successfully',
			results: {
				userSync: result?.insert_users_one,
				webhookLog: logResult?.insert_webhook_logs_one,
				retrievedUser: retrievedUser
			}
		});
		
	} catch (err) {
		console.error('Test error:', err);
		return json({
			success: false,
			error: err instanceof Error ? err.message : 'Unknown error',
			details: err
		}, { status: 500 });
	}
};
