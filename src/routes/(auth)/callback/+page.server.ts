import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { dbService } from '$lib/graphql/database-service';
import { isValidUser } from '$lib/types/auth';

export const load: PageServerLoad = async ({ locals, url, cookies }) => {
	console.log('=== CALLBACK FLOW START ===');
	
	// Check if user is authenticated
	if (!locals.user || !isValidUser(locals.user)) {
		console.log('No authenticated user found, redirecting to signin');
		throw redirect(302, '/signin');
	}

	const logtoUserId = locals.user.sub;
	console.log('Processing callback for user:', logtoUserId);

	// Get redirect target - prioritize explicit redirects first
	const redirectFromCookie = cookies.get('auth_redirect_to');
	const redirectToParam = url.searchParams.get('redirectTo');
	
	console.log('Redirect sources:', {
		cookie: redirectFromCookie,
		urlParam: redirectToParam
	});

	// If there's an explicit redirect, use it (but validate it's safe)
	if (redirectFromCookie || redirectToParam) {
		const explicitRedirect = redirectFromCookie || redirectToParam;
		
		// Validate redirect URL is safe (internal only)
		if (explicitRedirect && (
			explicitRedirect.startsWith('/') && 
			!explicitRedirect.startsWith('//') &&
			!explicitRedirect.includes('..'))
		) {
			console.log('Using explicit redirect:', explicitRedirect);
			cookies.delete('auth_redirect_to', { path: '/' });
			throw redirect(302, explicitRedirect);
		}
	}

	// Default onboarding flow - determine user state
	try {
		console.log('Starting onboarding flow analysis...');
		
		// Sync/get user from database first
		let dbUser = await dbService.getUserByLogtoId(logtoUserId);
		
		if (!dbUser) {
			console.log('User not in database, syncing from Logto...');
			await syncUserFromLogto(locals.user);
			dbUser = await dbService.getUserByLogtoId(logtoUserId);
		}

		if (!dbUser) {
			console.error('Failed to sync user to database');
			throw redirect(302, '/signin?error=sync_failed');
		}

		// Get comprehensive onboarding status
		const onboardingStatus = await dbService.getUserOnboardingStatus(dbUser.id);
		console.log('Onboarding status:', {
			profileComplete: onboardingStatus.profileComplete,
			hasOrganizations: onboardingStatus.hasOrganizations,
			missingFields: onboardingStatus.missingProfileFields
		});

		// Determine redirect based on onboarding status
		let finalRedirect: string;
		
		// Check if user has organizations
		if (!onboardingStatus.hasOrganizations) {
			console.log('No organizations found for user, redirecting to organization setup.');
			finalRedirect = '/organization/setup';
		} else {
			finalRedirect = determineOnboardingRedirect(onboardingStatus);
		}
		
		console.log('=== CALLBACK FLOW END ===');
		console.log('Final redirect:', finalRedirect);
		
		// Clear any redirect cookies
		cookies.delete('auth_redirect_to', { path: '/' });
		
		throw redirect(302, finalRedirect);

	} catch (error) {
		console.error('Error in callback flow:', error);
		
		// If it's already a redirect, re-throw it
		if (error instanceof Response && error.status >= 300 && error.status < 400) {
			throw error;
		}
		
		// For other errors, fallback to a safe redirect
		console.log('Fallback to dashboard due to error');
		cookies.delete('auth_redirect_to', { path: '/' });
		throw redirect(302, '/dashboard?onboarding=incomplete');
	}
};

/**
 * Sync user data from Logto to our database
 */
async function syncUserFromLogto(logtoUser: any) {
	try {
		await dbService.syncUser({
			id: logtoUser.sub,
			email: logtoUser.email || logtoUser.primaryEmail,
			username: logtoUser.username,
			name: logtoUser.name,
			avatar: logtoUser.picture || logtoUser.avatar,
			primaryEmail: logtoUser.email || logtoUser.primaryEmail,
			lastSignInAt: Date.now(),
			createdAt: Date.now(),
			updatedAt: Date.now()
		});
		console.log('User successfully synced to database');
	} catch (error) {
		console.error('Failed to sync user:', error);
		throw error;
	}
}

/**
 * Determine where to redirect user based on onboarding status
 */
function determineOnboardingRedirect(status: any): string {
	// Priority 1: Profile incomplete
	if (!status.profileComplete) {
		console.log('Profile incomplete, redirecting to profile completion');
		return '/profile/complete';
	}
	
	// Priority 2: No organizations
	if (!status.hasOrganizations) {
		console.log('No organizations, redirecting to organization setup');
		return '/organization/setup';
	}
	
	// Priority 3: All complete - go to dashboard
	console.log('Onboarding complete, redirecting to dashboard');
	return '/dashboard';
}
