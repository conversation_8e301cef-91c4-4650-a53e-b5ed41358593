import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, url, cookies }) => {
	console.log('=== SIGNIN PAGE LOAD ===');
	console.log('User already authenticated:', !!locals.user);
	
	// If user is already authenticated, redirect to dashboard
	if (locals.user) {
		const redirectTo = url.searchParams.get('redirect') || '/dashboard';
		console.log('User already signed in, redirecting to:', redirectTo);
		throw redirect(302, redirectTo);
	}

	// Get the redirect URL from query params for after sign-in
	const redirectTo = url.searchParams.get('redirect') || '/organization/setup';
	console.log('Starting sign-in process, will redirect to:', redirectTo, 'after callback');
	
	// Store redirect target in a cookie for the callback
	cookies.set('auth_redirect_to', redirectTo, { 
		path: '/', 
		maxAge: 60 * 10, // 10 minutes
		secure: false, // Set to true in production with HTTPS
		httpOnly: true 
	});
	
	// Use callback URL - this is required for Logto to set locals.user
	const callbackUrl = `${url.origin}/callback`;
	console.log('Callback URL:', callbackUrl);
	console.log('Redirect stored in cookie:', redirectTo);
	
	// Redirect to Logto sign-in with callback
	await locals.logtoClient.signIn(callbackUrl);
};