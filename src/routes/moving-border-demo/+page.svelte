<script>
  import { But<PERSON> } from '$lib/components/ui/moving-border';
</script>

<div class="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 p-4">
  <div class="space-y-8">
    <h1 class="text-3xl font-bold text-center">Moving Border Demo</h1>
    <div class="flex flex-wrap gap-4 justify-center">
      <Button
        borderRadius="1.75rem"
        class="bg-white dark:bg-slate-900 text-black dark:text-white border-neutral-200 dark:border-slate-800"
      >
        Borders are cool
      </Button>
      
      <Button
        borderRadius="0.5rem"
        duration={3000}
        class="bg-indigo-500/10 backdrop-blur-sm text-indigo-600 dark:text-indigo-300 border-indigo-300/20 dark:border-indigo-800/30"
        borderClass="bg-[radial-gradient(var(--indigo-500)_40%,transparent_60%)]"
      >
        Custom Colors
      </Button>
      
      <Button
        borderRadius="1rem"
        duration={1500}
        class="bg-emerald-500/10 backdrop-blur-sm text-emerald-600 dark:text-emerald-300 border-emerald-300/20 dark:border-emerald-800/30"
        borderClass="bg-[radial-gradient(var(--emerald-500)_40%,transparent_60%)]"
      >
        Fast Animation
      </Button>
    </div>
  </div>
</div>
