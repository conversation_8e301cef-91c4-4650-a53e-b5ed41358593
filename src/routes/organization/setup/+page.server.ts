import type { Actions, PageServerLoad } from './$types';
import { redirect, fail } from '@sveltejs/kit';
import { DatabaseService } from '$lib/graphql/database-service';
import { LOGTO_ADMIN_ENDPOINT, LOGTO_M2M_APP_ID, LOGTO_M2M_APP_SECRET } from '$env/static/private';
import { z } from 'zod';
import { isValidUser, getUserEmail } from '$lib/types/auth';

const createOrgSchema = z.object({
	orgName: z.string().min(1, 'Organization name is required').max(100),
	orgType: z.enum(['hiring', 'bench_sales', 'both']),
	description: z.string().max(500).optional(),
	emailDomain: z.string().optional()
});

const joinOrgSchema = z.object({
	joinCode: z.string().min(1, 'Join code or domain is required')
});

export const load: PageServerLoad = async ({ locals }) => {
	// Ensure user is authenticated
	if (!locals.user || !isValidUser(locals.user)) {
		throw redirect(302, '/signin');
	}

	try {
		// Get user from database
		const dbService = new DatabaseService();
		const dbUser = await dbService.getUserByLogtoId(locals.user.sub);
		
		if (!dbUser) {
			console.error('User not found in database during organization setup');
			throw redirect(302, '/profile/complete');
		}

		// Check if profile is complete
		const profileStatus = await dbService.checkUserProfileCompleteness(dbUser.id);
		
		if (!profileStatus.isComplete) {
			throw redirect(302, '/profile/complete');
		}

		// Check if user already has organizations
		const organizations = await dbService.getUserOrganizations(dbUser.id);
		
		if (organizations.length > 0) {
			throw redirect(302, '/dashboard');
		}

		// Check for pending invitations and auto-join opportunities
		const userEmail = getUserEmail(locals.user);
		let autoJoinOrganization = null;
		let pendingInvitations = [];

		if (userEmail) {
			const emailDomain = userEmail.split('@')[1]?.toLowerCase();
			if (emailDomain) {
				try {
					autoJoinOrganization = await dbService.getOrganizationByDomain(emailDomain);
				} catch (error) {
					console.log('No auto-join organization found for domain:', emailDomain);
				}
			}

			// TODO: Get pending invitations for this email
			// pendingInvitations = await dbService.getPendingInvitationsForEmail(userEmail);
		}

		return {
			user: locals.user,
			dbUser,
			autoJoinOrganization,
			pendingInvitations,
			userEmail
		};
		
	} catch (error) {
		console.error('Error loading organization setup page:', error);
		
		// If it's already a redirect, re-throw it
		if (error instanceof Response && error.status >= 300 && error.status < 400) {
			throw error;
		}
		
		throw redirect(302, '/profile/complete');
	}
};

export const actions: Actions = {
	createOrganization: async ({ request, locals }) => {
		console.log('Create organization action called');
		
		if (!locals.user || !isValidUser(locals.user)) {
			throw redirect(302, '/signin');
		}

		const userId = locals.user.sub as string;

		try {
			const formData = await request.formData();
			const data = {
				orgName: formData.get('orgName'),
				orgType: formData.get('orgType'),
				description: formData.get('description'),
				emailDomain: formData.get('emailDomain')
			};

			// Validate form data
			const validatedData = createOrgSchema.parse(data);
			console.log('Validated organization data:', validatedData);

			// Helper function to get access token for Logto Management API
			const getManagementApiToken = async () => {
				const tokenEndpoint = `${LOGTO_ADMIN_ENDPOINT}/oidc/token`;
				const response = await fetch(tokenEndpoint, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded',
						'Authorization': `Basic ${Buffer.from(`${LOGTO_M2M_APP_ID}:${LOGTO_M2M_APP_SECRET}`).toString('base64')}`
					},
					body: new URLSearchParams({
						grant_type: 'client_credentials',
						resource: `${LOGTO_ADMIN_ENDPOINT}/api`,
						scope: 'all'
					}).toString()
				});

				if (!response.ok) {
					throw new Error(`Failed to get management API token: ${response.statusText}`);
				}

				const tokenData = await response.json();
				return tokenData.access_token;
			};

			// Get access token for management API
			const accessToken = await getManagementApiToken();

			// Create organization in Logto using management API
			const createOrgResponse = await fetch(`${LOGTO_ADMIN_ENDPOINT}/api/organizations`, {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${accessToken}`,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					name: validatedData.orgName,
					description: validatedData.description || `${validatedData.orgType} organization`
				})
			});

			if (!createOrgResponse.ok) {
				throw new Error(`Failed to create organization in Logto: ${createOrgResponse.statusText}`);
			}

			const logtoOrg = await createOrgResponse.json();
			console.log('Created Logto organization:', logtoOrg);

			// Initialize database service
			const dbService = new DatabaseService();

			// Create organization in database
			const dbOrg = await dbService.insertOrganization({
				logto_id: logtoOrg.id,
				name: validatedData.orgName,
				org_type: validatedData.orgType,
				description: validatedData.description,
				settings: {
					features: {
						hiring: validatedData.orgType === 'hiring' || validatedData.orgType === 'both',
						bench_sales: validatedData.orgType === 'bench_sales' || validatedData.orgType === 'both'
					}
				}
			});

			console.log('Created database organization:', dbOrg);

			// Add email domain if provided
			if (validatedData.emailDomain) {
				await dbService.insertOrganizationDomain({
					organization_id: dbOrg.id,
					domain: validatedData.emailDomain.toLowerCase(),
					auto_join_enabled: true
				});
				console.log('Added email domain:', validatedData.emailDomain);
			}

			// Add user as admin to organization in Logto using Management API
			const addUserResponse = await fetch(`${LOGTO_ADMIN_ENDPOINT}/api/organizations/${logtoOrg.id}/users`, {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${accessToken}`,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					userIds: [userId]
				})
			});

			if (!addUserResponse.ok) {
				console.warn(`Failed to add user to Logto organization: ${addUserResponse.statusText}`);
			} else {
				console.log('User added to Logto organization');
			}

			// Create user-organization relationship in database
			await dbService.insertUserOrganization({
				user_id: userId,
				organization_id: dbOrg.id,
				role: 'admin',
				status: 'active'
			});

			console.log('Successfully created organization and assigned user as admin');
			throw redirect(302, '/dashboard');

		} catch (error) {
			console.error('Error creating organization:', error);
			
			if (error instanceof z.ZodError) {
				return fail(400, {
					error: 'Invalid form data',
					details: error.errors
				});
			}

			return fail(500, {
				error: 'Failed to create organization. Please try again.'
			});
		}
	},

	autoJoin: async ({ request, locals }) => {
		console.log('Auto-join organization action called');
		
		if (!locals.user || !isValidUser(locals.user)) {
			throw redirect(302, '/signin');
		}

		try {
			const formData = await request.formData();
			const organizationId = formData.get('organizationId') as string;
			
			if (!organizationId) {
				return fail(400, {
					error: 'Organization ID is required'
				});
			}

			// Get user from database
			const dbService = new DatabaseService();
			const dbUser = await dbService.getUserByLogtoId(locals.user.sub);
			
			if (!dbUser) {
				return fail(400, {
					error: 'User not found in database'
				});
			}

			// Create user-organization relationship
			await dbService.insertUserOrganization({
				user_id: dbUser.id,
				organization_id: organizationId,
				role: 'member',
				status: 'active'
			});

			console.log('Successfully auto-joined organization');
			throw redirect(302, '/dashboard');

		} catch (error) {
			console.error('Error auto-joining organization:', error);
			
			if (error instanceof Response && error.status >= 300 && error.status < 400) {
				throw error;
			}

			return fail(500, {
				error: 'Failed to join organization. Please try again.'
			});
		}
	}
};
