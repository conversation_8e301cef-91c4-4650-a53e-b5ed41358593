<script lang="ts">
	import type { PageData, ActionData } from './$types';
	import { enhance } from '$app/forms';
	import { page } from '$app/stores';
	
	// Props using Svelte 5 runes
	let { data }: { data: PageData } = $props();
	
	// Component state using runes
	let selectedOrgType = $state('both');
	let showCreateForm = $state(false);
	let showJoinForm = $state(false);
	let isSubmitting = $state(false);
	
	// Get action data for form errors
	const actionData: ActionData = $page.form as ActionData;
	
	// Form data state
	let createFormData = $state({
		orgName: '',
		description: '',
		emailDomain: ''
	});
	
	let joinFormData = $state({
		joinCode: ''
	});
	
	// Auto-fill domain if available
	$effect(() => {
		if (data.userEmail && !createFormData.emailDomain) {
			const domain = data.userEmail.split('@')[1];
			if (domain && !showCreateForm) {
				createFormData.emailDomain = domain;
			}
		}
	});
	
	// Form submission handlers
	function handleCreateSubmit() {
		isSubmitting = true;
	}
	
	function handleJoinSubmit() {
		isSubmitting = true;
	}
	
	function handleAutoJoin() {
		isSubmitting = true;
		// This would trigger a form submission for auto-join
	}
</script>

<svelte:head>
	<title>Organization Setup - SourceFlex</title>
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
	<div class="sm:mx-auto sm:w-full sm:max-w-2xl">
		<!-- Header -->
		<div class="text-center mb-8">
			<div class="mx-auto h-12 w-12 bg-indigo-600 rounded-lg flex items-center justify-center mb-4">
				<svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 3h10M9 21v-4a2 2 0 012-2h2a2 2 0 012 2v4" />
				</svg>
			</div>
			<h2 class="text-3xl font-bold text-gray-900">
				Welcome to SourceFlex!
			</h2>
			<p class="mt-2 text-lg text-gray-600">
				Let's set up your organization to get started with recruitment excellence
			</p>
		</div>

		<!-- User Info Card -->
		<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
			<div class="flex items-center space-x-3">
				<div class="flex-shrink-0">
					{#if data.user.picture}
						<img class="h-10 w-10 rounded-full" src={data.user.picture} alt="Profile" />
					{:else}
						<div class="h-10 w-10 bg-indigo-100 rounded-full flex items-center justify-center">
							<svg class="h-5 w-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
								<path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
							</svg>
						</div>
					{/if}
				</div>
				<div>
					<p class="text-sm font-medium text-gray-900">Signed in as</p>
					<p class="text-sm text-gray-600">{data.user?.email || data.user?.name || 'Unknown User'}</p>
				</div>
			</div>
		</div>

		<!-- Form Error -->
		{#if actionData?.error}
			<div class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
				<div class="flex">
					<svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
					</svg>
					<div class="ml-3">
						<p class="text-sm text-red-800">{actionData.error}</p>
					</div>
				</div>
			</div>
		{/if}

		<!-- Auto-Join Option (if available) -->
		{#if data.autoJoinOrganization}
			<div class="bg-green-50 border-2 border-green-200 rounded-lg p-6 mb-6">
				<div class="text-center">
					<div class="mx-auto h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
						<svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
						</svg>
					</div>
					<h3 class="text-lg font-semibold text-green-900 mb-2">Organization Found!</h3>
					<p class="text-green-700 mb-4">
						Your email domain matches <strong>{data.autoJoinOrganization.organization?.name}</strong>. 
						You can join automatically.
					</p>
					<form method="POST" action="?/autoJoin" use:enhance={handleAutoJoin}>
						<input type="hidden" name="organizationId" value={data.autoJoinOrganization.organization_id} />
						<button
							type="submit"
							disabled={isSubmitting}
							class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-400 transition-colors"
						>
							{#if isSubmitting}
								<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
									<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
									<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
								</svg>
								Joining...
							{:else}
								Join Organization Automatically
							{/if}
						</button>
					</form>
				</div>
			</div>

			<!-- Divider -->
			<div class="relative mb-6">
				<div class="absolute inset-0 flex items-center">
					<div class="w-full border-t border-gray-300"></div>
				</div>
				<div class="relative flex justify-center text-sm">
					<span class="px-2 bg-gray-50 text-gray-500">or choose another option</span>
				</div>
			</div>
		{/if}

		<!-- Main Content -->
		<div class="bg-white shadow-xl rounded-lg overflow-hidden">
			<div class="px-6 py-8">
				<!-- Organization Options -->
				<div class="space-y-6">
					<!-- Create New Organization -->
					<div class="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-indigo-400 transition-colors">
						<div class="text-center">
							<div class="mx-auto h-12 w-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
								<svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
								</svg>
							</div>
							<h3 class="text-lg font-semibold text-gray-900 mb-2">Create New Organization</h3>
							<p class="text-gray-600 mb-4">
								Start fresh with a new organization. You'll be the admin with full control.
							</p>
							<button
								onclick={() => showCreateForm = !showCreateForm}
								class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
							>
								{showCreateForm ? 'Cancel' : 'Create Organization'}
							</button>
						</div>
						
						{#if showCreateForm}
							<div class="mt-6 border-t pt-6">
								<form method="POST" action="?/createOrganization" use:enhance={handleCreateSubmit} class="space-y-4">
									<!-- Organization Name -->
									<div>
										<label for="orgName" class="block text-sm font-medium text-gray-700 mb-1">
											Organization Name *
										</label>
										<input
											type="text"
											id="orgName"
											name="orgName"
											bind:value={createFormData.orgName}
											required
											disabled={isSubmitting}
											class="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm disabled:bg-gray-50"
											placeholder="e.g., Acme Recruiting Solutions"
										/>
									</div>

									<!-- Organization Type -->
									<div role="group" aria-labelledby="orgTypeLabel">
										<div id="orgTypeLabel" class="block text-sm font-medium text-gray-700 mb-3">
											Organization Type *
										</div>
										<div class="grid grid-cols-1 gap-3 sm:grid-cols-3">
											<!-- Hiring -->
											<label class="relative flex cursor-pointer rounded-lg border bg-white p-4 shadow-sm focus:outline-none {selectedOrgType === 'hiring' ? 'border-indigo-600 ring-2 ring-indigo-600' : 'border-gray-300'}">
												<input type="radio" name="orgType" value="hiring" bind:group={selectedOrgType} class="sr-only" disabled={isSubmitting} />
												<span class="flex flex-1 flex-col">
													<span class="block text-sm font-medium text-gray-900">Hiring Team</span>
													<span class="mt-1 flex items-center text-sm text-gray-500">Post jobs, find candidates</span>
												</span>
											</label>

											<!-- Bench Sales -->
											<label class="relative flex cursor-pointer rounded-lg border bg-white p-4 shadow-sm focus:outline-none {selectedOrgType === 'bench_sales' ? 'border-indigo-600 ring-2 ring-indigo-600' : 'border-gray-300'}">
												<input type="radio" name="orgType" value="bench_sales" bind:group={selectedOrgType} class="sr-only" disabled={isSubmitting} />
												<span class="flex flex-1 flex-col">
													<span class="block text-sm font-medium text-gray-900">Bench Sales</span>
													<span class="mt-1 flex items-center text-sm text-gray-500">Manage bench, place candidates</span>
												</span>
											</label>

											<!-- Both -->
											<label class="relative flex cursor-pointer rounded-lg border bg-white p-4 shadow-sm focus:outline-none {selectedOrgType === 'both' ? 'border-indigo-600 ring-2 ring-indigo-600' : 'border-gray-300'}">
												<input type="radio" name="orgType" value="both" bind:group={selectedOrgType} class="sr-only" disabled={isSubmitting} />
												<span class="flex flex-1 flex-col">
													<span class="block text-sm font-medium text-gray-900">Full Service</span>
													<span class="mt-1 flex items-center text-sm text-gray-500">Complete recruitment solution</span>
												</span>
											</label>
										</div>
									</div>

									<!-- Description -->
									<div>
										<label for="description" class="block text-sm font-medium text-gray-700 mb-1">
											Description (Optional)
										</label>
										<textarea
											id="description"
											name="description"
											bind:value={createFormData.description}
											rows="3"
											disabled={isSubmitting}
											class="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm disabled:bg-gray-50"
											placeholder="Brief description of your organization..."
										></textarea>
									</div>

									<!-- Email Domain -->
									<div>
										<label for="emailDomain" class="block text-sm font-medium text-gray-700 mb-1">
											Company Email Domain (Optional)
										</label>
										<input
											type="text"
											id="emailDomain"
											name="emailDomain"
											bind:value={createFormData.emailDomain}
											disabled={isSubmitting}
											class="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm disabled:bg-gray-50"
											placeholder="e.g., acme.com"
										/>
										<p class="mt-1 text-xs text-gray-500">Team members with this email domain can auto-join your organization</p>
									</div>

									<div class="flex space-x-3">
										<button
											type="submit"
											disabled={isSubmitting}
											class="flex-1 flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 transition-colors"
										>
											{#if isSubmitting}
												<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
													<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
													<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
												</svg>
												Creating...
											{:else}
												Create Organization
											{/if}
										</button>
										<button
											type="button"
											disabled={isSubmitting}
											onclick={() => showCreateForm = false}
											class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-50 transition-colors"
										>
											Cancel
										</button>
									</div>
								</form>
							</div>
						{/if}
					</div>

					<!-- Divider -->
					<div class="relative">
						<div class="absolute inset-0 flex items-center">
							<div class="w-full border-t border-gray-300"></div>
						</div>
						<div class="relative flex justify-center text-sm">
							<span class="px-2 bg-white text-gray-500">or</span>
						</div>
					</div>

					<!-- Join Existing Organization -->
					<div class="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-indigo-400 transition-colors">
						<div class="text-center">
							<div class="mx-auto h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
								<svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
								</svg>
							</div>
							<h3 class="text-lg font-semibold text-gray-900 mb-2">Join Existing Organization</h3>
							<p class="text-gray-600 mb-4">
								Have an invitation or know your organization's details?
							</p>
							<button
								onclick={() => showJoinForm = !showJoinForm}
								class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
							>
								{showJoinForm ? 'Cancel' : 'Join Organization'}
							</button>
						</div>

						{#if showJoinForm}
							<div class="mt-6 border-t pt-6">
								<form method="POST" action="?/joinOrganization" use:enhance={handleJoinSubmit} class="space-y-4">
									<div>
										<label for="joinCode" class="block text-sm font-medium text-gray-700 mb-1">
											Invitation Code or Email Domain
										</label>
										<input
											type="text"
											id="joinCode"
											name="joinCode"
											bind:value={joinFormData.joinCode}
											required
											disabled={isSubmitting}
											class="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm disabled:bg-gray-50"
											placeholder="e.g., INV-ABC123 or acme.com"
										/>
										<p class="mt-1 text-xs text-gray-500">Enter your invitation code or your company's email domain</p>
									</div>

									<div class="flex space-x-3">
										<button
											type="submit"
											disabled={isSubmitting}
											class="flex-1 flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-400 transition-colors"
										>
											{#if isSubmitting}
												<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
													<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
													<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
												</svg>
												Joining...
											{:else}
												Join Organization
											{/if}
										</button>
										<button
											type="button"
											disabled={isSubmitting}
											onclick={() => showJoinForm = false}
											class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-50 transition-colors"
										>
											Cancel
										</button>
									</div>
								</form>
							</div>
						{/if}
					</div>
				</div>
			</div>

			<!-- Footer -->
			<div class="bg-gray-50 px-6 py-4">
				<div class="text-center">
					<p class="text-xs text-gray-500">
						Need help? Contact support or check if your organization has domain-based auto-join enabled.
					</p>
					<p class="text-xs text-gray-400 mt-1">
						Your data is secure and encrypted. We follow enterprise-grade security practices.
					</p>
				</div>
			</div>
		</div>
	</div>
</div>
