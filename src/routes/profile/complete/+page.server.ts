import type { Actions, PageServerLoad } from './$types';
import { redirect, fail } from '@sveltejs/kit';
import { dbService } from '$lib/graphql/database-service';
import { isValidUser } from '$lib/types/auth';
import { z } from 'zod';

const profileSchema = z.object({
	firstName: z.string().min(1, 'First name is required').max(50),
	lastName: z.string().min(1, 'Last name is required').max(50),
	jobTitle: z.string().min(1, 'Job title is required').max(100),
	company: z.string().min(1, 'Company is required').max(100),
	phone: z.string().optional(),
	bio: z.string().max(500).optional(),
	timezone: z.string().optional()
});

export const load: PageServerLoad = async ({ locals }) => {
	// Ensure user is authenticated
	if (!locals.user || !isValidUser(locals.user)) {
		throw redirect(302, '/signin');
	}

	try {
		// Get user from database
		const dbUser = await dbService.getUserByLogtoId(locals.user.sub);
		
		if (!dbUser) {
			console.error('User not found in database during profile completion');
			throw redirect(302, '/signin?error=user_not_found');
		}

		// Check current profile status
		const profileStatus = await dbService.checkUserProfileCompleteness(dbUser.id);
		
		// If profile is already complete, redirect to next step
		if (profileStatus.isComplete) {
			const onboardingStatus = await dbService.getUserOnboardingStatus(dbUser.id);
			
			if (onboardingStatus.hasOrganizations) {
				throw redirect(302, '/dashboard');
			} else {
				throw redirect(302, '/organization/setup');
			}
		}

		return {
			user: locals.user,
			currentProfile: profileStatus.user,
			missingFields: profileStatus.missingFields
		};
		
	} catch (error) {
		console.error('Error loading profile completion page:', error);
		
		// If it's already a redirect, re-throw it
		if (error instanceof Response && error.status >= 300 && error.status < 400) {
			throw error;
		}
		
		throw redirect(302, '/dashboard?error=profile_load_failed');
	}
};

export const actions: Actions = {
	complete: async ({ request, locals }) => {
		if (!locals.user || !isValidUser(locals.user)) {
			throw redirect(302, '/signin');
		}

		try {
			const formData = await request.formData();
			const data = {
				firstName: formData.get('firstName'),
				lastName: formData.get('lastName'),
				jobTitle: formData.get('jobTitle'),
				company: formData.get('company'),
				phone: formData.get('phone'),
				bio: formData.get('bio'),
				timezone: formData.get('timezone')
			};

			// Validate form data
			const validatedData = profileSchema.parse(data);
			
			// Get user from database
			const dbUser = await dbService.getUserByLogtoId(locals.user.sub);
			
			if (!dbUser) {
				return fail(500, {
					error: 'User not found in database'
				});
			}

			// Update user profile
			await dbService.updateUserProfile(dbUser.id, {
				first_name: validatedData.firstName,
				last_name: validatedData.lastName,
				job_title: validatedData.jobTitle,
				company: validatedData.company,
				phone: validatedData.phone || null,
				bio: validatedData.bio || null,
				timezone: validatedData.timezone || null,
				markCompleted: true
			});

			console.log('Profile completed successfully for user:', dbUser.id);

			// Check if user has organizations to determine next step
			const organizations = await dbService.getUserOrganizations(dbUser.id);
			
			if (organizations.length > 0) {
				throw redirect(302, '/dashboard');
			} else {
				throw redirect(302, '/organization/setup');
			}

		} catch (error) {
			console.error('Error completing profile:', error);
			
			if (error instanceof z.ZodError) {
				return fail(400, {
					error: 'Please check the form for errors',
					fieldErrors: error.errors.reduce((acc, err) => {
						const field = err.path[0];
						acc[field] = err.message;
						return acc;
					}, {} as Record<string, string>)
				});
			}

			if (error instanceof Response && error.status >= 300 && error.status < 400) {
				throw error;
			}

			return fail(500, {
				error: 'Failed to complete profile. Please try again.'
			});
		}
	}
};
