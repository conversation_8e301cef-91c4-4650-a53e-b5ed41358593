<script lang="ts">
	import { enhance } from '$app/forms';
	import { page } from '$app/stores';
	import type { PageData, ActionData } from './$types';
	
	// Props using Svelte 5 runes
	let { data }: { data: PageData } = $props();
	
	// Form state using runes
	let formData = $state({
		firstName: data.currentProfile?.first_name || '',
		lastName: data.currentProfile?.last_name || '',
		jobTitle: data.currentProfile?.job_title || '',
		company: data.currentProfile?.company || '',
		phone: data.currentProfile?.phone || '',
		bio: data.currentProfile?.bio || '',
		timezone: data.currentProfile?.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone
	});
	
	// Form validation state
	let isSubmitting = $state(false);
	let fieldErrors = $state<Record<string, string>>({});
	
	// Get action data for form errors - handle both error types
	const actionData = $page.form as { 
		error?: string; 
		fieldErrors?: Record<string, string>; 
	} | null;
	
	// Update field errors when action data changes
	$effect(() => {
		if (actionData?.fieldErrors) {
			fieldErrors = actionData.fieldErrors;
		} else {
			fieldErrors = {};
		}
	});
	
	// Progress calculation
	const requiredFields = ['firstName', 'lastName', 'jobTitle', 'company'];
	const progress = $derived(() => {
		const completed = requiredFields.filter(field => 
			formData[field as keyof typeof formData]?.trim()
		).length;
		return Math.round((completed / requiredFields.length) * 100);
	});
	
	// Form submission handler
	function handleSubmit() {
		isSubmitting = true;
		// Reset field errors
		fieldErrors = {};
	}
	
	// Timezone options (common ones)
	const timezones = [
		'America/New_York',
		'America/Chicago', 
		'America/Denver',
		'America/Los_Angeles',
		'America/Toronto',
		'Europe/London',
		'Europe/Paris',
		'Europe/Berlin',
		'Asia/Tokyo',
		'Asia/Shanghai',
		'Asia/Kolkata',
		'Australia/Sydney'
	];
</script>

<svelte:head>
	<title>Complete Your Profile - SourceFlex</title>
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
	<div class="sm:mx-auto sm:w-full sm:max-w-2xl">
		<!-- Header -->
		<div class="text-center mb-8">
			<div class="mx-auto h-12 w-12 bg-indigo-600 rounded-lg flex items-center justify-center mb-4">
				<svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
				</svg>
			</div>
			<h2 class="text-3xl font-bold text-gray-900">
				Complete Your Profile
			</h2>
			<p class="mt-2 text-lg text-gray-600">
				Help your team know you better with a complete profile
			</p>
		</div>

		<!-- Progress Bar -->
		<div class="mb-8">
			<div class="flex justify-between text-sm text-gray-600 mb-2">
				<span>Profile completion</span>
				<span>{progress()}%</span>
			</div>
			<div class="w-full bg-gray-200 rounded-full h-2">
				<div 
					class="bg-indigo-600 h-2 rounded-full transition-all duration-300 ease-out"
					style="width: {progress()}%"
				></div>
			</div>
		</div>

		<!-- User Info Card -->
		<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
			<div class="flex items-center space-x-3">
				<div class="flex-shrink-0">
					{#if data.user.picture}
						<img class="h-10 w-10 rounded-full" src={data.user.picture} alt="Profile" />
					{:else}
						<div class="h-10 w-10 bg-indigo-100 rounded-full flex items-center justify-center">
							<svg class="h-5 w-5 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
								<path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
							</svg>
						</div>
					{/if}
				</div>
				<div>
					<p class="text-sm font-medium text-gray-900">Signed in as</p>
					<p class="text-sm text-gray-600">{data.user.email || data.user.name || 'Unknown User'}</p>
				</div>
			</div>
		</div>

		<!-- Form Error -->
		{#if actionData?.error}
			<div class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
				<div class="flex">
					<svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
					</svg>
					<div class="ml-3">
						<p class="text-sm text-red-800">{actionData.error}</p>
					</div>
				</div>
			</div>
		{/if}

		<!-- Profile Form -->
		<div class="bg-white shadow-xl rounded-lg overflow-hidden">
			<form method="POST" action="?/complete" use:enhance={handleSubmit} class="px-6 py-8 space-y-6">
				<!-- Name Fields -->
				<div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
					<div>
						<label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">
							First Name *
						</label>
						<input
							type="text"
							id="firstName"
							name="firstName"
							bind:value={formData.firstName}
							required
							disabled={isSubmitting}
							class="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm disabled:bg-gray-50"
							class:border-red-500={fieldErrors.firstName}
						/>
						{#if fieldErrors.firstName}
							<p class="mt-1 text-sm text-red-600">{fieldErrors.firstName}</p>
						{/if}
					</div>

					<div>
						<label for="lastName" class="block text-sm font-medium text-gray-700 mb-1">
							Last Name *
						</label>
						<input
							type="text"
							id="lastName"
							name="lastName"
							bind:value={formData.lastName}
							required
							disabled={isSubmitting}
							class="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm disabled:bg-gray-50"
							class:border-red-500={fieldErrors.lastName}
						/>
						{#if fieldErrors.lastName}
							<p class="mt-1 text-sm text-red-600">{fieldErrors.lastName}</p>
						{/if}
					</div>
				</div>

				<!-- Professional Info -->
				<div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
					<div>
						<label for="jobTitle" class="block text-sm font-medium text-gray-700 mb-1">
							Job Title *
						</label>
						<input
							type="text"
							id="jobTitle"
							name="jobTitle"
							bind:value={formData.jobTitle}
							required
							disabled={isSubmitting}
							placeholder="e.g., Senior Recruiter"
							class="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm disabled:bg-gray-50"
							class:border-red-500={fieldErrors.jobTitle}
						/>
						{#if fieldErrors.jobTitle}
							<p class="mt-1 text-sm text-red-600">{fieldErrors.jobTitle}</p>
						{/if}
					</div>

					<div>
						<label for="company" class="block text-sm font-medium text-gray-700 mb-1">
							Company *
						</label>
						<input
							type="text"
							id="company"
							name="company"
							bind:value={formData.company}
							required
							disabled={isSubmitting}
							placeholder="e.g., Acme Recruiting"
							class="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm disabled:bg-gray-50"
							class:border-red-500={fieldErrors.company}
						/>
						{#if fieldErrors.company}
							<p class="mt-1 text-sm text-red-600">{fieldErrors.company}</p>
						{/if}
					</div>
				</div>

				<!-- Contact Info -->
				<div>
					<label for="phone" class="block text-sm font-medium text-gray-700 mb-1">
						Phone Number (Optional)
					</label>
					<input
						type="tel"
						id="phone"
						name="phone"
						bind:value={formData.phone}
						disabled={isSubmitting}
						placeholder="e.g., +****************"
						class="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm disabled:bg-gray-50"
					/>
				</div>

				<!-- Timezone -->
				<div>
					<label for="timezone" class="block text-sm font-medium text-gray-700 mb-1">
						Timezone (Optional)
					</label>
					<select
						id="timezone"
						name="timezone"
						bind:value={formData.timezone}
						disabled={isSubmitting}
						class="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm disabled:bg-gray-50"
					>
						<option value="">Select timezone...</option>
						{#each timezones as tz}
							<option value={tz}>{tz.replace('_', ' ')}</option>
						{/each}
					</select>
				</div>

				<!-- Bio -->
				<div>
					<label for="bio" class="block text-sm font-medium text-gray-700 mb-1">
						Bio (Optional)
					</label>
					<textarea
						id="bio"
						name="bio"
						bind:value={formData.bio}
						rows="3"
						disabled={isSubmitting}
						placeholder="Tell your team a bit about yourself..."
						class="block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm disabled:bg-gray-50"
					></textarea>
					<p class="mt-1 text-xs text-gray-500">Maximum 500 characters</p>
				</div>

				<!-- Submit Button -->
				<div class="pt-4">
					<button
						type="submit"
						disabled={isSubmitting || progress() < 100}
						class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
					>
						{#if isSubmitting}
							<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
								<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
								<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
							</svg>
							Completing Profile...
						{:else if progress() < 100}
							Complete Required Fields ({requiredFields.length - Math.floor(progress() / 25)} remaining)
						{:else}
							Complete Profile & Continue
						{/if}
					</button>
				</div>

				<!-- Helper Text -->
				<div class="text-center">
					<p class="text-xs text-gray-500">
						* Required fields must be completed to continue
					</p>
				</div>
			</form>
		</div>

		<!-- Footer -->
		<div class="mt-8 text-center">
			<p class="text-xs text-gray-500">
				Your profile information is secure and only visible to your organization members.
			</p>
		</div>
	</div>
</div>
