import { redirect } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	console.log('=== MAIN PAGE LOAD ===');
	console.log('User:', !!locals.user);
	
	// If user is already authenticated, redirect to dashboard
	if (locals.user) {
		console.log('User already authenticated, redirecting to dashboard');
		throw redirect(302, '/dashboard');
	}
	
	// Return empty object for marketing page
	return {};
};

export const actions: Actions = {
	signOut: async ({ locals, url }) => {
		const postLogoutRedirectUri = `${url.origin}/`;
		await locals.logtoClient.signOut(postLogoutRedirectUri);
	}
};
