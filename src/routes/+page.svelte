<script>
	import { onMount } from 'svelte';
	
	let heroVisible = false;
	let featuresVisible = false;
	
	onMount(() => {
		setTimeout(() => heroVisible = true, 100);
		setTimeout(() => featuresVisible = true, 500);
	});
</script>

<svelte:head>
	<title>SourceFlex - AI-Powered Hiring Management Platform</title>
	<meta name="description" content="Streamline multi-channel hiring with AI-powered tools. Find, screen, and manage talent with automated workflows, analytics, and bias-free processes." />
</svelte:head>

<!-- Hero Section -->
<section class="relative min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-purple-900 overflow-hidden">
	<!-- Animated background elements -->
	<div class="absolute inset-0 opacity-20">
		<div class="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
		<div class="absolute top-3/4 right-1/4 w-64 h-64 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-2000"></div>
		<div class="absolute bottom-1/4 left-1/3 w-64 h-64 bg-indigo-400 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-4000"></div>
	</div>
	
	<!-- Navigation -->
	<nav class="relative z-10 flex justify-between items-center px-6 py-4 bg-white/10 backdrop-blur-lg border-b border-white/20">
		<div class="flex items-center space-x-2">
			<div class="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
				<span class="text-white font-bold text-lg">S</span>
			</div>
			<span class="text-white font-bold text-xl">SourceFlex</span>
		</div>
		
		<div class="flex space-x-4">
			<a href="/signin" 
			   class="px-6 py-2 bg-white/20 text-white border border-white/30 rounded-lg hover:bg-white/30 transition-all duration-300 font-medium">
				Business Login
			</a>
			<a href="/signin" 
			   class="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300 font-medium shadow-lg">
				Candidate Portal
			</a>
		</div>
	</nav>
	
	<!-- Hero Content -->
	<div class="relative z-10 flex items-center justify-center min-h-screen px-6">
		<div class="max-w-4xl text-center">
			<div class="transform transition-all duration-1000 {heroVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}">
				<h1 class="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
					AI-Powered
					<span class="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
						Hiring
					</span>
					<br/>Revolution
				</h1>
				
				<p class="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
					Streamline multi-channel hiring with intelligent AI agents that find, screen, and manage talent automatically. 
					Reduce time-to-hire by 75% while eliminating bias.
				</p>
				
				<div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
					<a href="/signin" 
					   class="group px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-semibold text-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
						Start Free Trial
						<span class="ml-2 group-hover:translate-x-1 transition-transform duration-300">→</span>
					</a>
					<button class="px-8 py-4 border-2 border-white/30 text-white rounded-xl font-semibold text-lg hover:bg-white/10 transition-all duration-300">
						Watch Demo
					</button>
				</div>
			</div>
		</div>
	</div>
	
	<!-- Scroll indicator -->
	<div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
		<div class="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
			<div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
		</div>
	</div>
</section>

<!-- Features Section -->
<section class="py-20 bg-gradient-to-b from-gray-50 to-white">
	<div class="max-w-7xl mx-auto px-6">
		<div class="text-center mb-16">
			<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
				Why Choose SourceFlex?
			</h2>
			<p class="text-xl text-gray-600 max-w-3xl mx-auto">
				Our AI-powered platform transforms how you discover, evaluate, and hire top talent across multiple channels.
			</p>
		</div>
		
		<div class="transform transition-all duration-1000 {featuresVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}">
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
				<!-- AI-Powered Screening -->
				<div class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:-translate-y-2">
					<div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">AI-Powered Screening</h3>
					<p class="text-gray-600 leading-relaxed">
						Automatically screen thousands of resumes in minutes. Our AI analyzes skills, experience, and cultural fit with 95% accuracy, eliminating unconscious bias.
					</p>
				</div>
				
				<!-- Multi-Channel Sourcing -->
				<div class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:-translate-y-2">
					<div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9V3m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">Multi-Channel Sourcing</h3>
					<p class="text-gray-600 leading-relaxed">
						Source talent from LinkedIn, job boards, social media, and internal databases simultaneously. One platform, unlimited reach.
					</p>
				</div>
				
				<!-- Smart Analytics -->
				<div class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:-translate-y-2">
					<div class="w-16 h-16 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">Smart Analytics</h3>
					<p class="text-gray-600 leading-relaxed">
						Real-time insights into hiring metrics, candidate quality, and team performance. Make data-driven decisions to optimize your recruitment strategy.
					</p>
				</div>
				
				<!-- Bench Talent Management -->
				<div class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:-translate-y-2">
					<div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">Bench Talent Pool</h3>
					<p class="text-gray-600 leading-relaxed">
						Maintain a curated pool of pre-screened candidates ready for immediate placement. Reduce time-to-hire from weeks to days.
					</p>
				</div>
				
				<!-- Automated Workflows -->
				<div class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:-translate-y-2">
					<div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">Automated Workflows</h3>
					<p class="text-gray-600 leading-relaxed">
						From initial application to offer letter, automate repetitive tasks. Email sequences, interview scheduling, and follow-ups happen automatically.
					</p>
				</div>
				
				<!-- Compliance & Security -->
				<div class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:-translate-y-2">
					<div class="w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
						</svg>
					</div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">Compliance Ready</h3>
					<p class="text-gray-600 leading-relaxed">
						Built-in GDPR, EEOC, and accessibility compliance. Audit trails, data protection, and bias-free algorithms ensure legal peace of mind.
					</p>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Stats Section -->
<section class="py-20 bg-gradient-to-r from-blue-900 to-purple-900">
	<div class="max-w-7xl mx-auto px-6">
		<div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
			<div class="text-white">
				<div class="text-4xl md:text-5xl font-bold mb-2">75%</div>
				<div class="text-blue-200">Faster Hiring</div>
			</div>
			<div class="text-white">
				<div class="text-4xl md:text-5xl font-bold mb-2">95%</div>
				<div class="text-blue-200">Screening Accuracy</div>
			</div>
			<div class="text-white">
				<div class="text-4xl md:text-5xl font-bold mb-2">60%</div>
				<div class="text-blue-200">Cost Reduction</div>
			</div>
			<div class="text-white">
				<div class="text-4xl md:text-5xl font-bold mb-2">24/7</div>
				<div class="text-blue-200">AI Assistant</div>
			</div>
		</div>
	</div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-white">
	<div class="max-w-4xl mx-auto text-center px-6">
		<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
			Ready to Transform Your Hiring?
		</h2>
		<p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
			Join thousands of companies using SourceFlex to build better teams faster with AI-powered recruitment.
		</p>
		
		<div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
			<a href="/signin" 
			   class="group px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-semibold text-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
				Get Started Free
				<span class="ml-2 group-hover:translate-x-1 transition-transform duration-300">→</span>
			</a>
			<a href="/signin" 
			   class="px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-all duration-300">
				Schedule Demo
			</a>
		</div>
		
		<p class="text-sm text-gray-500 mt-6">
			14-day free trial • No credit card required • Setup in minutes
		</p>
	</div>
</section>

<!-- Footer -->
<footer class="bg-gray-900 text-white py-12">
	<div class="max-w-7xl mx-auto px-6">
		<div class="flex flex-col md:flex-row justify-between items-center">
			<div class="flex items-center space-x-2 mb-4 md:mb-0">
				<div class="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
					<span class="text-white font-bold text-lg">S</span>
				</div>
				<span class="text-white font-bold text-xl">SourceFlex</span>
			</div>
			
			<div class="flex space-x-6 text-gray-400">
				<button class="hover:text-white transition-colors">Privacy</button>
				<button class="hover:text-white transition-colors">Terms</button>
				<button class="hover:text-white transition-colors">Support</button>
				<button class="hover:text-white transition-colors">Contact</button>
			</div>
		</div>
		
		<div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
			<p>&copy; 2025 SourceFlex. All rights reserved. | AI-Powered Hiring Management Platform</p>
		</div>
	</div>
</footer>

<style>
	.animation-delay-2000 {
		animation-delay: 2s;
	}
	
	.animation-delay-4000 {
		animation-delay: 4s;
	}
	
	@keyframes pulse {
		0%, 100% {
			opacity: 0.4;
		}
		50% {
			opacity: 0.8;
		}
	}
</style>
