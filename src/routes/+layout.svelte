<script>
	// Import Tailwind CSS
	import '../styles/app.css';
	
	// TODO: Enable URQL setup for GraphQL database connections when needed
	// import { browser } from '$app/environment';
	// 
	// if (browser) {
	// 	import('@urql/svelte').then(({ setClient }) => {
	// 		import('$lib/graphql-client').then(({ createGraphQLClient }) => {
	// 			const client = createGraphQLClient();
	// 			setClient(client);
	// 		});
	// 	});
	// }
</script>

<slot />
