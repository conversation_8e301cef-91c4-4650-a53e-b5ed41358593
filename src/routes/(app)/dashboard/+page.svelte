<script lang="ts">
	import type { PageData } from './$types';
	import { Button } from 'bits-ui';
	
	export let data: PageData;
</script>

<div class="dashboard">
	<header class="dashboard-header">
		<h1 class="text-3xl font-semibold text-gray-900">Dashboard</h1>
		<p class="text-gray-600">Welcome back, {data.user?.name || data.user?.primaryEmail || 'User'}</p>
	</header>
	
	<div class="dashboard-content">
		<!-- Stats Section -->
		<section class="stats-section">
			<div class="stats-grid">
				<div class="stat-card">
					<div class="stat-header">
						<h3 class="text-sm font-medium text-gray-600">Active Jobs</h3>
					</div>
					<div class="stat-value">
						<span class="text-2xl font-bold text-gray-900">12</span>
					</div>
					<div class="stat-change positive">
						<span class="text-xs text-green-600">+2 this week</span>
					</div>
					<div class="stat-description">
						<span class="text-xs text-gray-500">Job postings currently active</span>
					</div>
				</div>
				
				<div class="stat-card">
					<div class="stat-header">
						<h3 class="text-sm font-medium text-gray-600">Applications</h3>
					</div>
					<div class="stat-value">
						<span class="text-2xl font-bold text-gray-900">234</span>
					</div>
					<div class="stat-change positive">
						<span class="text-xs text-green-600">+15 today</span>
					</div>
					<div class="stat-description">
						<span class="text-xs text-gray-500">Total applications received</span>
					</div>
				</div>
				
				<div class="stat-card">
					<div class="stat-header">
						<h3 class="text-sm font-medium text-gray-600">Interviews</h3>
					</div>
					<div class="stat-value">
						<span class="text-2xl font-bold text-gray-900">8</span>
					</div>
					<div class="stat-change neutral">
						<span class="text-xs text-blue-600">3 scheduled</span>
					</div>
					<div class="stat-description">
						<span class="text-xs text-gray-500">Upcoming interviews this week</span>
					</div>
				</div>
			</div>
		</section>
		
		<!-- Recent Activity -->
		<section class="activity-section">
			<h2 class="text-xl font-medium text-gray-900 mb-4">Recent Activity</h2>
			<div class="activity-list">
				<div class="activity-item">
					<div class="activity-icon bg-green-100 text-green-600">
						<span>✓</span>
					</div>
					<div class="activity-content">
						<div class="activity-title">New application received</div>
						<div class="activity-time">2 hours ago</div>
					</div>
					<div class="status-indicator bg-yellow-100 text-yellow-800">medium</div>
				</div>
				
				<div class="activity-item">
					<div class="activity-icon bg-blue-100 text-blue-600">
						<span>📅</span>
					</div>
					<div class="activity-content">
						<div class="activity-title">Interview scheduled</div>
						<div class="activity-time">4 hours ago</div>
					</div>
					<div class="status-indicator bg-red-100 text-red-800">high</div>
				</div>
				
				<div class="activity-item">
					<div class="activity-icon bg-purple-100 text-purple-600">
						<span>💼</span>
					</div>
					<div class="activity-content">
						<div class="activity-title">Job posted successfully</div>
						<div class="activity-time">1 day ago</div>
					</div>
					<div class="status-indicator bg-green-100 text-green-800">low</div>
				</div>
			</div>
		</section>
		
		<!-- Quick Actions -->
		<section class="actions-section">
			<h2 class="text-xl font-medium text-gray-900 mb-4">Quick Actions</h2>
			<div class="action-buttons">
				<Button.Root class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors">
					Post New Job
				</Button.Root>
				<Button.Root class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md font-medium border border-gray-300 transition-colors">
					Review Applications
				</Button.Root>
				<Button.Root class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-md font-medium border border-gray-300 transition-colors">
					Schedule Interview
				</Button.Root>
			</div>
		</section>
	</div>
</div>

<style>
	.dashboard {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem;
	}
	
	.dashboard-header {
		margin-bottom: 2rem;
	}
	
	.dashboard-content {
		display: grid;
		gap: 2rem;
	}
	
	.stats-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 1rem;
	}
	
	.stat-card {
		background: white;
		border: 1px solid #e5e7eb;
		border-radius: 8px;
		padding: 1.5rem;
		transition: all 0.2s ease;
	}
	
	.stat-card:hover {
		border-color: #d1d5db;
		box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
		transform: translateY(-1px);
	}
	
	.stat-header {
		margin-bottom: 0.5rem;
	}
	
	.stat-value {
		margin-bottom: 0.5rem;
	}
	
	.stat-change {
		display: flex;
		align-items: center;
		gap: 0.25rem;
		margin-bottom: 0.75rem;
	}
	
	.activity-section,
	.actions-section {
		background: white;
		border: 1px solid #e5e7eb;
		border-radius: 8px;
		padding: 1.5rem;
	}
	
	.activity-list {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}
	
	.activity-item {
		display: flex;
		align-items: flex-start;
		gap: 1rem;
		padding: 1rem;
		border-radius: 6px;
		background: #f9fafb;
		border: 1px solid #f3f4f6;
		transition: all 0.2s ease;
	}
	
	.activity-item:hover {
		background: white;
		border-color: #e5e7eb;
		transform: translateY(-1px);
	}
	
	.activity-icon {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 2rem;
		height: 2rem;
		border-radius: 50%;
		font-size: 0.875rem;
	}
	
	.activity-content {
		flex: 1;
	}
	
	.activity-title {
		font-weight: 500;
		font-size: 1rem;
		color: #111827;
		margin-bottom: 0.25rem;
	}
	
	.activity-time {
		color: #6b7280;
		font-size: 0.875rem;
	}
	
	.status-indicator {
		padding: 0.25rem 0.75rem;
		border-radius: 9999px;
		font-size: 0.75rem;
		font-weight: 500;
		text-transform: capitalize;
	}
	
	.action-buttons {
		display: flex;
		gap: 1rem;
		flex-wrap: wrap;
	}
</style>