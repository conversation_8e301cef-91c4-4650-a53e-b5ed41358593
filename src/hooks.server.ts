import { handleLogto, UserScope } from '@logto/sveltekit';
import { env } from '$env/dynamic/private';
import type { Handle } from '@sveltejs/kit';

const logtoHandle: Handle = handleLogto(
	{
		endpoint: env.LOGTO_ENDPOINT!,
		appId: env.LOGTO_APP_ID!,
		appSecret: env.LOGTO_APP_SECRET!,
		scopes: [
			UserScope.Email,
			UserScope.Profile,
			UserScope.Organizations,
			UserScope.OrganizationRoles
		]
		// Removed resources for now - we'll add them back when properly configured
	},
	{
		encryptionKey: env.LOGTO_COOKIE_ENCRYPTION_KEY!
	},
	{
		fetchUserInfo: true
	}
);

export const handle: Handle = logtoHandle;
