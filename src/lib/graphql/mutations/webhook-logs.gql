# Webhook logging queries and mutations

mutation LogWebhookEvent($log: webhook_logs_insert_input!) {
  insert_webhook_logs_one(object: $log) {
    id
    webhook_type
    logto_event_id
    payload
    processing_status
    error_message
    processed_at
    retry_count
    created_at
  }
}

query GetWebhookLogs($limit: Int = 50) {
  webhook_logs(
    order_by: { created_at: desc }
    limit: $limit
  ) {
    id
    webhook_type
    logto_event_id
    processing_status
    error_message
    processed_at
    retry_count
    created_at
  }
}
