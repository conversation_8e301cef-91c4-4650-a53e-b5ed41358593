# User management mutations

mutation InsertUser($user: users_insert_input!) {
  insert_users_one(object: $user) {
    id
    logto_user_id
    email
    username
    first_name
    last_name
    phone
    avatar_url
    email_verified
    is_active
    created_at
    updated_at
  }
}

mutation UpdateUser($logtoUserId: String!, $changes: users_set_input!) {
  update_users(
    where: { logto_user_id: { _eq: $logtoUserId } }
    _set: $changes
  ) {
    affected_rows
    returning {
      id
      logto_user_id
      email
      username
      first_name
      last_name
      phone
      avatar_url
      email_verified
      is_active
      last_login_at
      updated_at
    }
  }
}

mutation UpsertUser($user: users_insert_input!) {
  insert_users_one(
    object: $user
    on_conflict: {
      constraint: users_logto_user_id_key
      update_columns: [
        email, username, first_name, last_name, phone, 
        avatar_url, email_verified, is_active, last_login_at, updated_at
      ]
    }
  ) {
    id
    logto_user_id
    email
    username
    first_name
    last_name
    phone
    avatar_url
    email_verified
    is_active
    last_login_at
    created_at
    updated_at
  }
}

query GetUserByLogtoId($logtoUserId: String!) {
  users(where: { logto_user_id: { _eq: $logtoUserId } }) {
    id
    logto_user_id
    email
    username
    first_name
    last_name
    phone
    avatar_url
    email_verified
    is_active
    last_login_at
    created_at
    updated_at
  }
}
