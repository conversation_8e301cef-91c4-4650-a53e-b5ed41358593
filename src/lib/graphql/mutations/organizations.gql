# Organization management mutations

mutation InsertOrganization($organization: organizations_insert_input!) {
  insert_organizations_one(object: $organization) {
    id
    logto_org_id
    name
    slug
    description
    website_url
    email_domain
    organization_type
    settings
    is_active
    created_at
    updated_at
  }
}

mutation UpdateOrganization($logtoOrgId: uuid!, $changes: organizations_set_input!) {
  update_organizations(
    where: { logto_org_id: { _eq: $logtoOrgId } }
    _set: $changes
  ) {
    affected_rows
    returning {
      id
      logto_org_id
      name
      slug
      description
      website_url
      email_domain
      organization_type
      settings
      is_active
      updated_at
    }
  }
}

mutation InsertUserOrganization($membership: user_organizations_insert_input!) {
  insert_user_organizations_one(object: $membership) {
    id
    user_id
    organization_id
    role
    status
    invited_by
    invited_at
    joined_at
    created_at
    updated_at
  }
}

mutation UpdateUserOrganizationRole($userId: uuid!, $organizationId: uuid!, $changes: user_organizations_set_input!) {
  update_user_organizations(
    where: { 
      _and: [
        { user_id: { _eq: $userId } }
        { organization_id: { _eq: $organizationId } }
      ]
    }
    _set: $changes
  ) {
    affected_rows
    returning {
      id
      user_id
      organization_id
      role
      status
      updated_at
    }
  }
}

query GetOrganizationByLogtoId($logtoOrgId: uuid!) {
  organizations(where: { logto_org_id: { _eq: $logtoOrgId } }) {
    id
    logto_org_id
    name
    slug
    description
    website_url
    email_domain
    organization_type
    settings
    is_active
    created_at
    updated_at
  }
}

query GetUserOrganizations($userId: uuid!) {
  user_organizations(where: { user_id: { _eq: $userId } }) {
    id
    role
    status
    joined_at
    organization {
      id
      name
      slug
      description
      organization_type
      is_active
    }
  }
}
