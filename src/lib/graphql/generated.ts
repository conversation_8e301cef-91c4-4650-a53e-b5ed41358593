import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = {
	[_ in K]?: never;
};
export type Incremental<T> =
	| T
	| { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
	ID: { input: string; output: string };
	String: { input: string; output: string };
	Boolean: { input: boolean; output: boolean };
	Int: { input: number; output: number };
	Float: { input: number; output: number };
	inet: { input: any; output: any };
	jsonb: { input: any; output: any };
	timestamptz: { input: string; output: string };
	uuid: { input: string; output: string };
};

/** Boolean expression to compare columns of type "Boolean". All fields are combined with logical 'AND'. */
export type Boolean_Comparison_Exp = {
	_eq?: InputMaybe<Scalars['Boolean']['input']>;
	_gt?: InputMaybe<Scalars['Boolean']['input']>;
	_gte?: InputMaybe<Scalars['Boolean']['input']>;
	_in?: InputMaybe<Array<Scalars['Boolean']['input']>>;
	_is_null?: InputMaybe<Scalars['Boolean']['input']>;
	_lt?: InputMaybe<Scalars['Boolean']['input']>;
	_lte?: InputMaybe<Scalars['Boolean']['input']>;
	_neq?: InputMaybe<Scalars['Boolean']['input']>;
	_nin?: InputMaybe<Array<Scalars['Boolean']['input']>>;
};

/** Boolean expression to compare columns of type "Int". All fields are combined with logical 'AND'. */
export type Int_Comparison_Exp = {
	_eq?: InputMaybe<Scalars['Int']['input']>;
	_gt?: InputMaybe<Scalars['Int']['input']>;
	_gte?: InputMaybe<Scalars['Int']['input']>;
	_in?: InputMaybe<Array<Scalars['Int']['input']>>;
	_is_null?: InputMaybe<Scalars['Boolean']['input']>;
	_lt?: InputMaybe<Scalars['Int']['input']>;
	_lte?: InputMaybe<Scalars['Int']['input']>;
	_neq?: InputMaybe<Scalars['Int']['input']>;
	_nin?: InputMaybe<Array<Scalars['Int']['input']>>;
};

/** Boolean expression to compare columns of type "String". All fields are combined with logical 'AND'. */
export type String_Comparison_Exp = {
	_eq?: InputMaybe<Scalars['String']['input']>;
	_gt?: InputMaybe<Scalars['String']['input']>;
	_gte?: InputMaybe<Scalars['String']['input']>;
	/** does the column match the given case-insensitive pattern */
	_ilike?: InputMaybe<Scalars['String']['input']>;
	_in?: InputMaybe<Array<Scalars['String']['input']>>;
	/** does the column match the given POSIX regular expression, case insensitive */
	_iregex?: InputMaybe<Scalars['String']['input']>;
	_is_null?: InputMaybe<Scalars['Boolean']['input']>;
	/** does the column match the given pattern */
	_like?: InputMaybe<Scalars['String']['input']>;
	_lt?: InputMaybe<Scalars['String']['input']>;
	_lte?: InputMaybe<Scalars['String']['input']>;
	_neq?: InputMaybe<Scalars['String']['input']>;
	/** does the column NOT match the given case-insensitive pattern */
	_nilike?: InputMaybe<Scalars['String']['input']>;
	_nin?: InputMaybe<Array<Scalars['String']['input']>>;
	/** does the column NOT match the given POSIX regular expression, case insensitive */
	_niregex?: InputMaybe<Scalars['String']['input']>;
	/** does the column NOT match the given pattern */
	_nlike?: InputMaybe<Scalars['String']['input']>;
	/** does the column NOT match the given POSIX regular expression, case sensitive */
	_nregex?: InputMaybe<Scalars['String']['input']>;
	/** does the column NOT match the given SQL regular expression */
	_nsimilar?: InputMaybe<Scalars['String']['input']>;
	/** does the column match the given POSIX regular expression, case sensitive */
	_regex?: InputMaybe<Scalars['String']['input']>;
	/** does the column match the given SQL regular expression */
	_similar?: InputMaybe<Scalars['String']['input']>;
};

/** columns and relationships of "auth_audit_logs" */
export type Auth_Audit_Logs = {
	__typename?: 'auth_audit_logs';
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	event_type: Scalars['String']['output'];
	id: Scalars['uuid']['output'];
	ip_address?: Maybe<Scalars['inet']['output']>;
	metadata?: Maybe<Scalars['jsonb']['output']>;
	/** An object relationship */
	organization?: Maybe<Organizations>;
	organization_id?: Maybe<Scalars['uuid']['output']>;
	/** An object relationship */
	user?: Maybe<Users>;
	user_agent?: Maybe<Scalars['String']['output']>;
	user_id?: Maybe<Scalars['uuid']['output']>;
};

/** columns and relationships of "auth_audit_logs" */
export type Auth_Audit_LogsMetadataArgs = {
	path?: InputMaybe<Scalars['String']['input']>;
};

/** aggregated selection of "auth_audit_logs" */
export type Auth_Audit_Logs_Aggregate = {
	__typename?: 'auth_audit_logs_aggregate';
	aggregate?: Maybe<Auth_Audit_Logs_Aggregate_Fields>;
	nodes: Array<Auth_Audit_Logs>;
};

export type Auth_Audit_Logs_Aggregate_Bool_Exp = {
	count?: InputMaybe<Auth_Audit_Logs_Aggregate_Bool_Exp_Count>;
};

export type Auth_Audit_Logs_Aggregate_Bool_Exp_Count = {
	arguments?: InputMaybe<Array<Auth_Audit_Logs_Select_Column>>;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
	filter?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
	predicate: Int_Comparison_Exp;
};

/** aggregate fields of "auth_audit_logs" */
export type Auth_Audit_Logs_Aggregate_Fields = {
	__typename?: 'auth_audit_logs_aggregate_fields';
	count: Scalars['Int']['output'];
	max?: Maybe<Auth_Audit_Logs_Max_Fields>;
	min?: Maybe<Auth_Audit_Logs_Min_Fields>;
};

/** aggregate fields of "auth_audit_logs" */
export type Auth_Audit_Logs_Aggregate_FieldsCountArgs = {
	columns?: InputMaybe<Array<Auth_Audit_Logs_Select_Column>>;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
};

/** order by aggregate values of table "auth_audit_logs" */
export type Auth_Audit_Logs_Aggregate_Order_By = {
	count?: InputMaybe<Order_By>;
	max?: InputMaybe<Auth_Audit_Logs_Max_Order_By>;
	min?: InputMaybe<Auth_Audit_Logs_Min_Order_By>;
};

/** append existing jsonb value of filtered columns with new jsonb value */
export type Auth_Audit_Logs_Append_Input = {
	metadata?: InputMaybe<Scalars['jsonb']['input']>;
};

/** input type for inserting array relation for remote table "auth_audit_logs" */
export type Auth_Audit_Logs_Arr_Rel_Insert_Input = {
	data: Array<Auth_Audit_Logs_Insert_Input>;
	/** upsert condition */
	on_conflict?: InputMaybe<Auth_Audit_Logs_On_Conflict>;
};

/** Boolean expression to filter rows from the table "auth_audit_logs". All fields are combined with a logical 'AND'. */
export type Auth_Audit_Logs_Bool_Exp = {
	_and?: InputMaybe<Array<Auth_Audit_Logs_Bool_Exp>>;
	_not?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
	_or?: InputMaybe<Array<Auth_Audit_Logs_Bool_Exp>>;
	created_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	event_type?: InputMaybe<String_Comparison_Exp>;
	id?: InputMaybe<Uuid_Comparison_Exp>;
	ip_address?: InputMaybe<Inet_Comparison_Exp>;
	metadata?: InputMaybe<Jsonb_Comparison_Exp>;
	organization?: InputMaybe<Organizations_Bool_Exp>;
	organization_id?: InputMaybe<Uuid_Comparison_Exp>;
	user?: InputMaybe<Users_Bool_Exp>;
	user_agent?: InputMaybe<String_Comparison_Exp>;
	user_id?: InputMaybe<Uuid_Comparison_Exp>;
};

/** unique or primary key constraints on table "auth_audit_logs" */
export enum Auth_Audit_Logs_Constraint {
	/** unique or primary key constraint on columns "id" */
	AuthAuditLogsPkey = 'auth_audit_logs_pkey'
}

/** delete the field or element with specified path (for JSON arrays, negative integers count from the end) */
export type Auth_Audit_Logs_Delete_At_Path_Input = {
	metadata?: InputMaybe<Array<Scalars['String']['input']>>;
};

/** delete the array element with specified index (negative integers count from the end). throws an error if top level container is not an array */
export type Auth_Audit_Logs_Delete_Elem_Input = {
	metadata?: InputMaybe<Scalars['Int']['input']>;
};

/** delete key/value pair or string element. key/value pairs are matched based on their key value */
export type Auth_Audit_Logs_Delete_Key_Input = {
	metadata?: InputMaybe<Scalars['String']['input']>;
};

/** input type for inserting data into table "auth_audit_logs" */
export type Auth_Audit_Logs_Insert_Input = {
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	event_type?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	ip_address?: InputMaybe<Scalars['inet']['input']>;
	metadata?: InputMaybe<Scalars['jsonb']['input']>;
	organization?: InputMaybe<Organizations_Obj_Rel_Insert_Input>;
	organization_id?: InputMaybe<Scalars['uuid']['input']>;
	user?: InputMaybe<Users_Obj_Rel_Insert_Input>;
	user_agent?: InputMaybe<Scalars['String']['input']>;
	user_id?: InputMaybe<Scalars['uuid']['input']>;
};

/** aggregate max on columns */
export type Auth_Audit_Logs_Max_Fields = {
	__typename?: 'auth_audit_logs_max_fields';
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	event_type?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	organization_id?: Maybe<Scalars['uuid']['output']>;
	user_agent?: Maybe<Scalars['String']['output']>;
	user_id?: Maybe<Scalars['uuid']['output']>;
};

/** order by max() on columns of table "auth_audit_logs" */
export type Auth_Audit_Logs_Max_Order_By = {
	created_at?: InputMaybe<Order_By>;
	event_type?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	organization_id?: InputMaybe<Order_By>;
	user_agent?: InputMaybe<Order_By>;
	user_id?: InputMaybe<Order_By>;
};

/** aggregate min on columns */
export type Auth_Audit_Logs_Min_Fields = {
	__typename?: 'auth_audit_logs_min_fields';
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	event_type?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	organization_id?: Maybe<Scalars['uuid']['output']>;
	user_agent?: Maybe<Scalars['String']['output']>;
	user_id?: Maybe<Scalars['uuid']['output']>;
};

/** order by min() on columns of table "auth_audit_logs" */
export type Auth_Audit_Logs_Min_Order_By = {
	created_at?: InputMaybe<Order_By>;
	event_type?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	organization_id?: InputMaybe<Order_By>;
	user_agent?: InputMaybe<Order_By>;
	user_id?: InputMaybe<Order_By>;
};

/** response of any mutation on the table "auth_audit_logs" */
export type Auth_Audit_Logs_Mutation_Response = {
	__typename?: 'auth_audit_logs_mutation_response';
	/** number of rows affected by the mutation */
	affected_rows: Scalars['Int']['output'];
	/** data from the rows affected by the mutation */
	returning: Array<Auth_Audit_Logs>;
};

/** on_conflict condition type for table "auth_audit_logs" */
export type Auth_Audit_Logs_On_Conflict = {
	constraint: Auth_Audit_Logs_Constraint;
	update_columns?: Array<Auth_Audit_Logs_Update_Column>;
	where?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
};

/** Ordering options when selecting data from "auth_audit_logs". */
export type Auth_Audit_Logs_Order_By = {
	created_at?: InputMaybe<Order_By>;
	event_type?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	ip_address?: InputMaybe<Order_By>;
	metadata?: InputMaybe<Order_By>;
	organization?: InputMaybe<Organizations_Order_By>;
	organization_id?: InputMaybe<Order_By>;
	user?: InputMaybe<Users_Order_By>;
	user_agent?: InputMaybe<Order_By>;
	user_id?: InputMaybe<Order_By>;
};

/** primary key columns input for table: auth_audit_logs */
export type Auth_Audit_Logs_Pk_Columns_Input = {
	id: Scalars['uuid']['input'];
};

/** prepend existing jsonb value of filtered columns with new jsonb value */
export type Auth_Audit_Logs_Prepend_Input = {
	metadata?: InputMaybe<Scalars['jsonb']['input']>;
};

/** select columns of table "auth_audit_logs" */
export enum Auth_Audit_Logs_Select_Column {
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	EventType = 'event_type',
	/** column name */
	Id = 'id',
	/** column name */
	IpAddress = 'ip_address',
	/** column name */
	Metadata = 'metadata',
	/** column name */
	OrganizationId = 'organization_id',
	/** column name */
	UserAgent = 'user_agent',
	/** column name */
	UserId = 'user_id'
}

/** input type for updating data in table "auth_audit_logs" */
export type Auth_Audit_Logs_Set_Input = {
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	event_type?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	ip_address?: InputMaybe<Scalars['inet']['input']>;
	metadata?: InputMaybe<Scalars['jsonb']['input']>;
	organization_id?: InputMaybe<Scalars['uuid']['input']>;
	user_agent?: InputMaybe<Scalars['String']['input']>;
	user_id?: InputMaybe<Scalars['uuid']['input']>;
};

/** Streaming cursor of the table "auth_audit_logs" */
export type Auth_Audit_Logs_Stream_Cursor_Input = {
	/** Stream column input with initial value */
	initial_value: Auth_Audit_Logs_Stream_Cursor_Value_Input;
	/** cursor ordering */
	ordering?: InputMaybe<Cursor_Ordering>;
};

/** Initial value of the column from where the streaming should start */
export type Auth_Audit_Logs_Stream_Cursor_Value_Input = {
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	event_type?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	ip_address?: InputMaybe<Scalars['inet']['input']>;
	metadata?: InputMaybe<Scalars['jsonb']['input']>;
	organization_id?: InputMaybe<Scalars['uuid']['input']>;
	user_agent?: InputMaybe<Scalars['String']['input']>;
	user_id?: InputMaybe<Scalars['uuid']['input']>;
};

/** update columns of table "auth_audit_logs" */
export enum Auth_Audit_Logs_Update_Column {
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	EventType = 'event_type',
	/** column name */
	Id = 'id',
	/** column name */
	IpAddress = 'ip_address',
	/** column name */
	Metadata = 'metadata',
	/** column name */
	OrganizationId = 'organization_id',
	/** column name */
	UserAgent = 'user_agent',
	/** column name */
	UserId = 'user_id'
}

export type Auth_Audit_Logs_Updates = {
	/** append existing jsonb value of filtered columns with new jsonb value */
	_append?: InputMaybe<Auth_Audit_Logs_Append_Input>;
	/** delete the field or element with specified path (for JSON arrays, negative integers count from the end) */
	_delete_at_path?: InputMaybe<Auth_Audit_Logs_Delete_At_Path_Input>;
	/** delete the array element with specified index (negative integers count from the end). throws an error if top level container is not an array */
	_delete_elem?: InputMaybe<Auth_Audit_Logs_Delete_Elem_Input>;
	/** delete key/value pair or string element. key/value pairs are matched based on their key value */
	_delete_key?: InputMaybe<Auth_Audit_Logs_Delete_Key_Input>;
	/** prepend existing jsonb value of filtered columns with new jsonb value */
	_prepend?: InputMaybe<Auth_Audit_Logs_Prepend_Input>;
	/** sets the columns of the filtered rows to the given values */
	_set?: InputMaybe<Auth_Audit_Logs_Set_Input>;
	/** filter the rows which have to be updated */
	where: Auth_Audit_Logs_Bool_Exp;
};

/** ordering argument of a cursor */
export enum Cursor_Ordering {
	/** ascending ordering of the cursor */
	Asc = 'ASC',
	/** descending ordering of the cursor */
	Desc = 'DESC'
}

/** Boolean expression to compare columns of type "inet". All fields are combined with logical 'AND'. */
export type Inet_Comparison_Exp = {
	_eq?: InputMaybe<Scalars['inet']['input']>;
	_gt?: InputMaybe<Scalars['inet']['input']>;
	_gte?: InputMaybe<Scalars['inet']['input']>;
	_in?: InputMaybe<Array<Scalars['inet']['input']>>;
	_is_null?: InputMaybe<Scalars['Boolean']['input']>;
	_lt?: InputMaybe<Scalars['inet']['input']>;
	_lte?: InputMaybe<Scalars['inet']['input']>;
	_neq?: InputMaybe<Scalars['inet']['input']>;
	_nin?: InputMaybe<Array<Scalars['inet']['input']>>;
};

export type Jsonb_Cast_Exp = {
	String?: InputMaybe<String_Comparison_Exp>;
};

/** Boolean expression to compare columns of type "jsonb". All fields are combined with logical 'AND'. */
export type Jsonb_Comparison_Exp = {
	_cast?: InputMaybe<Jsonb_Cast_Exp>;
	/** is the column contained in the given json value */
	_contained_in?: InputMaybe<Scalars['jsonb']['input']>;
	/** does the column contain the given json value at the top level */
	_contains?: InputMaybe<Scalars['jsonb']['input']>;
	_eq?: InputMaybe<Scalars['jsonb']['input']>;
	_gt?: InputMaybe<Scalars['jsonb']['input']>;
	_gte?: InputMaybe<Scalars['jsonb']['input']>;
	/** does the string exist as a top-level key in the column */
	_has_key?: InputMaybe<Scalars['String']['input']>;
	/** do all of these strings exist as top-level keys in the column */
	_has_keys_all?: InputMaybe<Array<Scalars['String']['input']>>;
	/** do any of these strings exist as top-level keys in the column */
	_has_keys_any?: InputMaybe<Array<Scalars['String']['input']>>;
	_in?: InputMaybe<Array<Scalars['jsonb']['input']>>;
	_is_null?: InputMaybe<Scalars['Boolean']['input']>;
	_lt?: InputMaybe<Scalars['jsonb']['input']>;
	_lte?: InputMaybe<Scalars['jsonb']['input']>;
	_neq?: InputMaybe<Scalars['jsonb']['input']>;
	_nin?: InputMaybe<Array<Scalars['jsonb']['input']>>;
};

/** mutation root */
export type Mutation_Root = {
	__typename?: 'mutation_root';
	/** delete data from the table: "auth_audit_logs" */
	delete_auth_audit_logs?: Maybe<Auth_Audit_Logs_Mutation_Response>;
	/** delete single row from the table: "auth_audit_logs" */
	delete_auth_audit_logs_by_pk?: Maybe<Auth_Audit_Logs>;
	/** delete data from the table: "organization_domains" */
	delete_organization_domains?: Maybe<Organization_Domains_Mutation_Response>;
	/** delete single row from the table: "organization_domains" */
	delete_organization_domains_by_pk?: Maybe<Organization_Domains>;
	/** delete data from the table: "organization_invitations" */
	delete_organization_invitations?: Maybe<Organization_Invitations_Mutation_Response>;
	/** delete single row from the table: "organization_invitations" */
	delete_organization_invitations_by_pk?: Maybe<Organization_Invitations>;
	/** delete data from the table: "organizations" */
	delete_organizations?: Maybe<Organizations_Mutation_Response>;
	/** delete single row from the table: "organizations" */
	delete_organizations_by_pk?: Maybe<Organizations>;
	/** delete data from the table: "schema_migrations" */
	delete_schema_migrations?: Maybe<Schema_Migrations_Mutation_Response>;
	/** delete single row from the table: "schema_migrations" */
	delete_schema_migrations_by_pk?: Maybe<Schema_Migrations>;
	/** delete data from the table: "user_organizations" */
	delete_user_organizations?: Maybe<User_Organizations_Mutation_Response>;
	/** delete single row from the table: "user_organizations" */
	delete_user_organizations_by_pk?: Maybe<User_Organizations>;
	/** delete data from the table: "users" */
	delete_users?: Maybe<Users_Mutation_Response>;
	/** delete single row from the table: "users" */
	delete_users_by_pk?: Maybe<Users>;
	/** delete data from the table: "webhook_logs" */
	delete_webhook_logs?: Maybe<Webhook_Logs_Mutation_Response>;
	/** delete single row from the table: "webhook_logs" */
	delete_webhook_logs_by_pk?: Maybe<Webhook_Logs>;
	/** insert data into the table: "auth_audit_logs" */
	insert_auth_audit_logs?: Maybe<Auth_Audit_Logs_Mutation_Response>;
	/** insert a single row into the table: "auth_audit_logs" */
	insert_auth_audit_logs_one?: Maybe<Auth_Audit_Logs>;
	/** insert data into the table: "organization_domains" */
	insert_organization_domains?: Maybe<Organization_Domains_Mutation_Response>;
	/** insert a single row into the table: "organization_domains" */
	insert_organization_domains_one?: Maybe<Organization_Domains>;
	/** insert data into the table: "organization_invitations" */
	insert_organization_invitations?: Maybe<Organization_Invitations_Mutation_Response>;
	/** insert a single row into the table: "organization_invitations" */
	insert_organization_invitations_one?: Maybe<Organization_Invitations>;
	/** insert data into the table: "organizations" */
	insert_organizations?: Maybe<Organizations_Mutation_Response>;
	/** insert a single row into the table: "organizations" */
	insert_organizations_one?: Maybe<Organizations>;
	/** insert data into the table: "schema_migrations" */
	insert_schema_migrations?: Maybe<Schema_Migrations_Mutation_Response>;
	/** insert a single row into the table: "schema_migrations" */
	insert_schema_migrations_one?: Maybe<Schema_Migrations>;
	/** insert data into the table: "user_organizations" */
	insert_user_organizations?: Maybe<User_Organizations_Mutation_Response>;
	/** insert a single row into the table: "user_organizations" */
	insert_user_organizations_one?: Maybe<User_Organizations>;
	/** insert data into the table: "users" */
	insert_users?: Maybe<Users_Mutation_Response>;
	/** insert a single row into the table: "users" */
	insert_users_one?: Maybe<Users>;
	/** insert data into the table: "webhook_logs" */
	insert_webhook_logs?: Maybe<Webhook_Logs_Mutation_Response>;
	/** insert a single row into the table: "webhook_logs" */
	insert_webhook_logs_one?: Maybe<Webhook_Logs>;
	/** update data of the table: "auth_audit_logs" */
	update_auth_audit_logs?: Maybe<Auth_Audit_Logs_Mutation_Response>;
	/** update single row of the table: "auth_audit_logs" */
	update_auth_audit_logs_by_pk?: Maybe<Auth_Audit_Logs>;
	/** update multiples rows of table: "auth_audit_logs" */
	update_auth_audit_logs_many?: Maybe<Array<Maybe<Auth_Audit_Logs_Mutation_Response>>>;
	/** update data of the table: "organization_domains" */
	update_organization_domains?: Maybe<Organization_Domains_Mutation_Response>;
	/** update single row of the table: "organization_domains" */
	update_organization_domains_by_pk?: Maybe<Organization_Domains>;
	/** update multiples rows of table: "organization_domains" */
	update_organization_domains_many?: Maybe<Array<Maybe<Organization_Domains_Mutation_Response>>>;
	/** update data of the table: "organization_invitations" */
	update_organization_invitations?: Maybe<Organization_Invitations_Mutation_Response>;
	/** update single row of the table: "organization_invitations" */
	update_organization_invitations_by_pk?: Maybe<Organization_Invitations>;
	/** update multiples rows of table: "organization_invitations" */
	update_organization_invitations_many?: Maybe<
		Array<Maybe<Organization_Invitations_Mutation_Response>>
	>;
	/** update data of the table: "organizations" */
	update_organizations?: Maybe<Organizations_Mutation_Response>;
	/** update single row of the table: "organizations" */
	update_organizations_by_pk?: Maybe<Organizations>;
	/** update multiples rows of table: "organizations" */
	update_organizations_many?: Maybe<Array<Maybe<Organizations_Mutation_Response>>>;
	/** update data of the table: "schema_migrations" */
	update_schema_migrations?: Maybe<Schema_Migrations_Mutation_Response>;
	/** update single row of the table: "schema_migrations" */
	update_schema_migrations_by_pk?: Maybe<Schema_Migrations>;
	/** update multiples rows of table: "schema_migrations" */
	update_schema_migrations_many?: Maybe<Array<Maybe<Schema_Migrations_Mutation_Response>>>;
	/** update data of the table: "user_organizations" */
	update_user_organizations?: Maybe<User_Organizations_Mutation_Response>;
	/** update single row of the table: "user_organizations" */
	update_user_organizations_by_pk?: Maybe<User_Organizations>;
	/** update multiples rows of table: "user_organizations" */
	update_user_organizations_many?: Maybe<Array<Maybe<User_Organizations_Mutation_Response>>>;
	/** update data of the table: "users" */
	update_users?: Maybe<Users_Mutation_Response>;
	/** update single row of the table: "users" */
	update_users_by_pk?: Maybe<Users>;
	/** update multiples rows of table: "users" */
	update_users_many?: Maybe<Array<Maybe<Users_Mutation_Response>>>;
	/** update data of the table: "webhook_logs" */
	update_webhook_logs?: Maybe<Webhook_Logs_Mutation_Response>;
	/** update single row of the table: "webhook_logs" */
	update_webhook_logs_by_pk?: Maybe<Webhook_Logs>;
	/** update multiples rows of table: "webhook_logs" */
	update_webhook_logs_many?: Maybe<Array<Maybe<Webhook_Logs_Mutation_Response>>>;
};

/** mutation root */
export type Mutation_RootDelete_Auth_Audit_LogsArgs = {
	where: Auth_Audit_Logs_Bool_Exp;
};

/** mutation root */
export type Mutation_RootDelete_Auth_Audit_Logs_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

/** mutation root */
export type Mutation_RootDelete_Organization_DomainsArgs = {
	where: Organization_Domains_Bool_Exp;
};

/** mutation root */
export type Mutation_RootDelete_Organization_Domains_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

/** mutation root */
export type Mutation_RootDelete_Organization_InvitationsArgs = {
	where: Organization_Invitations_Bool_Exp;
};

/** mutation root */
export type Mutation_RootDelete_Organization_Invitations_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

/** mutation root */
export type Mutation_RootDelete_OrganizationsArgs = {
	where: Organizations_Bool_Exp;
};

/** mutation root */
export type Mutation_RootDelete_Organizations_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

/** mutation root */
export type Mutation_RootDelete_Schema_MigrationsArgs = {
	where: Schema_Migrations_Bool_Exp;
};

/** mutation root */
export type Mutation_RootDelete_Schema_Migrations_By_PkArgs = {
	id: Scalars['Int']['input'];
};

/** mutation root */
export type Mutation_RootDelete_User_OrganizationsArgs = {
	where: User_Organizations_Bool_Exp;
};

/** mutation root */
export type Mutation_RootDelete_User_Organizations_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

/** mutation root */
export type Mutation_RootDelete_UsersArgs = {
	where: Users_Bool_Exp;
};

/** mutation root */
export type Mutation_RootDelete_Users_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

/** mutation root */
export type Mutation_RootDelete_Webhook_LogsArgs = {
	where: Webhook_Logs_Bool_Exp;
};

/** mutation root */
export type Mutation_RootDelete_Webhook_Logs_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

/** mutation root */
export type Mutation_RootInsert_Auth_Audit_LogsArgs = {
	objects: Array<Auth_Audit_Logs_Insert_Input>;
	on_conflict?: InputMaybe<Auth_Audit_Logs_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_Auth_Audit_Logs_OneArgs = {
	object: Auth_Audit_Logs_Insert_Input;
	on_conflict?: InputMaybe<Auth_Audit_Logs_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_Organization_DomainsArgs = {
	objects: Array<Organization_Domains_Insert_Input>;
	on_conflict?: InputMaybe<Organization_Domains_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_Organization_Domains_OneArgs = {
	object: Organization_Domains_Insert_Input;
	on_conflict?: InputMaybe<Organization_Domains_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_Organization_InvitationsArgs = {
	objects: Array<Organization_Invitations_Insert_Input>;
	on_conflict?: InputMaybe<Organization_Invitations_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_Organization_Invitations_OneArgs = {
	object: Organization_Invitations_Insert_Input;
	on_conflict?: InputMaybe<Organization_Invitations_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_OrganizationsArgs = {
	objects: Array<Organizations_Insert_Input>;
	on_conflict?: InputMaybe<Organizations_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_Organizations_OneArgs = {
	object: Organizations_Insert_Input;
	on_conflict?: InputMaybe<Organizations_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_Schema_MigrationsArgs = {
	objects: Array<Schema_Migrations_Insert_Input>;
	on_conflict?: InputMaybe<Schema_Migrations_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_Schema_Migrations_OneArgs = {
	object: Schema_Migrations_Insert_Input;
	on_conflict?: InputMaybe<Schema_Migrations_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_User_OrganizationsArgs = {
	objects: Array<User_Organizations_Insert_Input>;
	on_conflict?: InputMaybe<User_Organizations_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_User_Organizations_OneArgs = {
	object: User_Organizations_Insert_Input;
	on_conflict?: InputMaybe<User_Organizations_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_UsersArgs = {
	objects: Array<Users_Insert_Input>;
	on_conflict?: InputMaybe<Users_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_Users_OneArgs = {
	object: Users_Insert_Input;
	on_conflict?: InputMaybe<Users_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_Webhook_LogsArgs = {
	objects: Array<Webhook_Logs_Insert_Input>;
	on_conflict?: InputMaybe<Webhook_Logs_On_Conflict>;
};

/** mutation root */
export type Mutation_RootInsert_Webhook_Logs_OneArgs = {
	object: Webhook_Logs_Insert_Input;
	on_conflict?: InputMaybe<Webhook_Logs_On_Conflict>;
};

/** mutation root */
export type Mutation_RootUpdate_Auth_Audit_LogsArgs = {
	_append?: InputMaybe<Auth_Audit_Logs_Append_Input>;
	_delete_at_path?: InputMaybe<Auth_Audit_Logs_Delete_At_Path_Input>;
	_delete_elem?: InputMaybe<Auth_Audit_Logs_Delete_Elem_Input>;
	_delete_key?: InputMaybe<Auth_Audit_Logs_Delete_Key_Input>;
	_prepend?: InputMaybe<Auth_Audit_Logs_Prepend_Input>;
	_set?: InputMaybe<Auth_Audit_Logs_Set_Input>;
	where: Auth_Audit_Logs_Bool_Exp;
};

/** mutation root */
export type Mutation_RootUpdate_Auth_Audit_Logs_By_PkArgs = {
	_append?: InputMaybe<Auth_Audit_Logs_Append_Input>;
	_delete_at_path?: InputMaybe<Auth_Audit_Logs_Delete_At_Path_Input>;
	_delete_elem?: InputMaybe<Auth_Audit_Logs_Delete_Elem_Input>;
	_delete_key?: InputMaybe<Auth_Audit_Logs_Delete_Key_Input>;
	_prepend?: InputMaybe<Auth_Audit_Logs_Prepend_Input>;
	_set?: InputMaybe<Auth_Audit_Logs_Set_Input>;
	pk_columns: Auth_Audit_Logs_Pk_Columns_Input;
};

/** mutation root */
export type Mutation_RootUpdate_Auth_Audit_Logs_ManyArgs = {
	updates: Array<Auth_Audit_Logs_Updates>;
};

/** mutation root */
export type Mutation_RootUpdate_Organization_DomainsArgs = {
	_set?: InputMaybe<Organization_Domains_Set_Input>;
	where: Organization_Domains_Bool_Exp;
};

/** mutation root */
export type Mutation_RootUpdate_Organization_Domains_By_PkArgs = {
	_set?: InputMaybe<Organization_Domains_Set_Input>;
	pk_columns: Organization_Domains_Pk_Columns_Input;
};

/** mutation root */
export type Mutation_RootUpdate_Organization_Domains_ManyArgs = {
	updates: Array<Organization_Domains_Updates>;
};

/** mutation root */
export type Mutation_RootUpdate_Organization_InvitationsArgs = {
	_set?: InputMaybe<Organization_Invitations_Set_Input>;
	where: Organization_Invitations_Bool_Exp;
};

/** mutation root */
export type Mutation_RootUpdate_Organization_Invitations_By_PkArgs = {
	_set?: InputMaybe<Organization_Invitations_Set_Input>;
	pk_columns: Organization_Invitations_Pk_Columns_Input;
};

/** mutation root */
export type Mutation_RootUpdate_Organization_Invitations_ManyArgs = {
	updates: Array<Organization_Invitations_Updates>;
};

/** mutation root */
export type Mutation_RootUpdate_OrganizationsArgs = {
	_append?: InputMaybe<Organizations_Append_Input>;
	_delete_at_path?: InputMaybe<Organizations_Delete_At_Path_Input>;
	_delete_elem?: InputMaybe<Organizations_Delete_Elem_Input>;
	_delete_key?: InputMaybe<Organizations_Delete_Key_Input>;
	_prepend?: InputMaybe<Organizations_Prepend_Input>;
	_set?: InputMaybe<Organizations_Set_Input>;
	where: Organizations_Bool_Exp;
};

/** mutation root */
export type Mutation_RootUpdate_Organizations_By_PkArgs = {
	_append?: InputMaybe<Organizations_Append_Input>;
	_delete_at_path?: InputMaybe<Organizations_Delete_At_Path_Input>;
	_delete_elem?: InputMaybe<Organizations_Delete_Elem_Input>;
	_delete_key?: InputMaybe<Organizations_Delete_Key_Input>;
	_prepend?: InputMaybe<Organizations_Prepend_Input>;
	_set?: InputMaybe<Organizations_Set_Input>;
	pk_columns: Organizations_Pk_Columns_Input;
};

/** mutation root */
export type Mutation_RootUpdate_Organizations_ManyArgs = {
	updates: Array<Organizations_Updates>;
};

/** mutation root */
export type Mutation_RootUpdate_Schema_MigrationsArgs = {
	_inc?: InputMaybe<Schema_Migrations_Inc_Input>;
	_set?: InputMaybe<Schema_Migrations_Set_Input>;
	where: Schema_Migrations_Bool_Exp;
};

/** mutation root */
export type Mutation_RootUpdate_Schema_Migrations_By_PkArgs = {
	_inc?: InputMaybe<Schema_Migrations_Inc_Input>;
	_set?: InputMaybe<Schema_Migrations_Set_Input>;
	pk_columns: Schema_Migrations_Pk_Columns_Input;
};

/** mutation root */
export type Mutation_RootUpdate_Schema_Migrations_ManyArgs = {
	updates: Array<Schema_Migrations_Updates>;
};

/** mutation root */
export type Mutation_RootUpdate_User_OrganizationsArgs = {
	_set?: InputMaybe<User_Organizations_Set_Input>;
	where: User_Organizations_Bool_Exp;
};

/** mutation root */
export type Mutation_RootUpdate_User_Organizations_By_PkArgs = {
	_set?: InputMaybe<User_Organizations_Set_Input>;
	pk_columns: User_Organizations_Pk_Columns_Input;
};

/** mutation root */
export type Mutation_RootUpdate_User_Organizations_ManyArgs = {
	updates: Array<User_Organizations_Updates>;
};

/** mutation root */
export type Mutation_RootUpdate_UsersArgs = {
	_set?: InputMaybe<Users_Set_Input>;
	where: Users_Bool_Exp;
};

/** mutation root */
export type Mutation_RootUpdate_Users_By_PkArgs = {
	_set?: InputMaybe<Users_Set_Input>;
	pk_columns: Users_Pk_Columns_Input;
};

/** mutation root */
export type Mutation_RootUpdate_Users_ManyArgs = {
	updates: Array<Users_Updates>;
};

/** mutation root */
export type Mutation_RootUpdate_Webhook_LogsArgs = {
	_append?: InputMaybe<Webhook_Logs_Append_Input>;
	_delete_at_path?: InputMaybe<Webhook_Logs_Delete_At_Path_Input>;
	_delete_elem?: InputMaybe<Webhook_Logs_Delete_Elem_Input>;
	_delete_key?: InputMaybe<Webhook_Logs_Delete_Key_Input>;
	_inc?: InputMaybe<Webhook_Logs_Inc_Input>;
	_prepend?: InputMaybe<Webhook_Logs_Prepend_Input>;
	_set?: InputMaybe<Webhook_Logs_Set_Input>;
	where: Webhook_Logs_Bool_Exp;
};

/** mutation root */
export type Mutation_RootUpdate_Webhook_Logs_By_PkArgs = {
	_append?: InputMaybe<Webhook_Logs_Append_Input>;
	_delete_at_path?: InputMaybe<Webhook_Logs_Delete_At_Path_Input>;
	_delete_elem?: InputMaybe<Webhook_Logs_Delete_Elem_Input>;
	_delete_key?: InputMaybe<Webhook_Logs_Delete_Key_Input>;
	_inc?: InputMaybe<Webhook_Logs_Inc_Input>;
	_prepend?: InputMaybe<Webhook_Logs_Prepend_Input>;
	_set?: InputMaybe<Webhook_Logs_Set_Input>;
	pk_columns: Webhook_Logs_Pk_Columns_Input;
};

/** mutation root */
export type Mutation_RootUpdate_Webhook_Logs_ManyArgs = {
	updates: Array<Webhook_Logs_Updates>;
};

/** column ordering options */
export enum Order_By {
	/** in ascending order, nulls last */
	Asc = 'asc',
	/** in ascending order, nulls first */
	AscNullsFirst = 'asc_nulls_first',
	/** in ascending order, nulls last */
	AscNullsLast = 'asc_nulls_last',
	/** in descending order, nulls first */
	Desc = 'desc',
	/** in descending order, nulls first */
	DescNullsFirst = 'desc_nulls_first',
	/** in descending order, nulls last */
	DescNullsLast = 'desc_nulls_last'
}

/** columns and relationships of "organization_domains" */
export type Organization_Domains = {
	__typename?: 'organization_domains';
	auto_join_enabled?: Maybe<Scalars['Boolean']['output']>;
	auto_join_role?: Maybe<Scalars['String']['output']>;
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	domain: Scalars['String']['output'];
	id: Scalars['uuid']['output'];
	/** An object relationship */
	organization?: Maybe<Organizations>;
	organization_id?: Maybe<Scalars['uuid']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
	verification_method?: Maybe<Scalars['String']['output']>;
	verification_status?: Maybe<Scalars['String']['output']>;
	verification_token?: Maybe<Scalars['String']['output']>;
	verified_at?: Maybe<Scalars['timestamptz']['output']>;
};

/** aggregated selection of "organization_domains" */
export type Organization_Domains_Aggregate = {
	__typename?: 'organization_domains_aggregate';
	aggregate?: Maybe<Organization_Domains_Aggregate_Fields>;
	nodes: Array<Organization_Domains>;
};

export type Organization_Domains_Aggregate_Bool_Exp = {
	bool_and?: InputMaybe<Organization_Domains_Aggregate_Bool_Exp_Bool_And>;
	bool_or?: InputMaybe<Organization_Domains_Aggregate_Bool_Exp_Bool_Or>;
	count?: InputMaybe<Organization_Domains_Aggregate_Bool_Exp_Count>;
};

export type Organization_Domains_Aggregate_Bool_Exp_Bool_And = {
	arguments: Organization_Domains_Select_Column_Organization_Domains_Aggregate_Bool_Exp_Bool_And_Arguments_Columns;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
	filter?: InputMaybe<Organization_Domains_Bool_Exp>;
	predicate: Boolean_Comparison_Exp;
};

export type Organization_Domains_Aggregate_Bool_Exp_Bool_Or = {
	arguments: Organization_Domains_Select_Column_Organization_Domains_Aggregate_Bool_Exp_Bool_Or_Arguments_Columns;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
	filter?: InputMaybe<Organization_Domains_Bool_Exp>;
	predicate: Boolean_Comparison_Exp;
};

export type Organization_Domains_Aggregate_Bool_Exp_Count = {
	arguments?: InputMaybe<Array<Organization_Domains_Select_Column>>;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
	filter?: InputMaybe<Organization_Domains_Bool_Exp>;
	predicate: Int_Comparison_Exp;
};

/** aggregate fields of "organization_domains" */
export type Organization_Domains_Aggregate_Fields = {
	__typename?: 'organization_domains_aggregate_fields';
	count: Scalars['Int']['output'];
	max?: Maybe<Organization_Domains_Max_Fields>;
	min?: Maybe<Organization_Domains_Min_Fields>;
};

/** aggregate fields of "organization_domains" */
export type Organization_Domains_Aggregate_FieldsCountArgs = {
	columns?: InputMaybe<Array<Organization_Domains_Select_Column>>;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
};

/** order by aggregate values of table "organization_domains" */
export type Organization_Domains_Aggregate_Order_By = {
	count?: InputMaybe<Order_By>;
	max?: InputMaybe<Organization_Domains_Max_Order_By>;
	min?: InputMaybe<Organization_Domains_Min_Order_By>;
};

/** input type for inserting array relation for remote table "organization_domains" */
export type Organization_Domains_Arr_Rel_Insert_Input = {
	data: Array<Organization_Domains_Insert_Input>;
	/** upsert condition */
	on_conflict?: InputMaybe<Organization_Domains_On_Conflict>;
};

/** Boolean expression to filter rows from the table "organization_domains". All fields are combined with a logical 'AND'. */
export type Organization_Domains_Bool_Exp = {
	_and?: InputMaybe<Array<Organization_Domains_Bool_Exp>>;
	_not?: InputMaybe<Organization_Domains_Bool_Exp>;
	_or?: InputMaybe<Array<Organization_Domains_Bool_Exp>>;
	auto_join_enabled?: InputMaybe<Boolean_Comparison_Exp>;
	auto_join_role?: InputMaybe<String_Comparison_Exp>;
	created_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	domain?: InputMaybe<String_Comparison_Exp>;
	id?: InputMaybe<Uuid_Comparison_Exp>;
	organization?: InputMaybe<Organizations_Bool_Exp>;
	organization_id?: InputMaybe<Uuid_Comparison_Exp>;
	updated_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	verification_method?: InputMaybe<String_Comparison_Exp>;
	verification_status?: InputMaybe<String_Comparison_Exp>;
	verification_token?: InputMaybe<String_Comparison_Exp>;
	verified_at?: InputMaybe<Timestamptz_Comparison_Exp>;
};

/** unique or primary key constraints on table "organization_domains" */
export enum Organization_Domains_Constraint {
	/** unique or primary key constraint on columns "domain", "organization_id" */
	OrganizationDomainsOrganizationIdDomainKey = 'organization_domains_organization_id_domain_key',
	/** unique or primary key constraint on columns "id" */
	OrganizationDomainsPkey = 'organization_domains_pkey'
}

/** input type for inserting data into table "organization_domains" */
export type Organization_Domains_Insert_Input = {
	auto_join_enabled?: InputMaybe<Scalars['Boolean']['input']>;
	auto_join_role?: InputMaybe<Scalars['String']['input']>;
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	domain?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	organization?: InputMaybe<Organizations_Obj_Rel_Insert_Input>;
	organization_id?: InputMaybe<Scalars['uuid']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
	verification_method?: InputMaybe<Scalars['String']['input']>;
	verification_status?: InputMaybe<Scalars['String']['input']>;
	verification_token?: InputMaybe<Scalars['String']['input']>;
	verified_at?: InputMaybe<Scalars['timestamptz']['input']>;
};

/** aggregate max on columns */
export type Organization_Domains_Max_Fields = {
	__typename?: 'organization_domains_max_fields';
	auto_join_role?: Maybe<Scalars['String']['output']>;
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	domain?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	organization_id?: Maybe<Scalars['uuid']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
	verification_method?: Maybe<Scalars['String']['output']>;
	verification_status?: Maybe<Scalars['String']['output']>;
	verification_token?: Maybe<Scalars['String']['output']>;
	verified_at?: Maybe<Scalars['timestamptz']['output']>;
};

/** order by max() on columns of table "organization_domains" */
export type Organization_Domains_Max_Order_By = {
	auto_join_role?: InputMaybe<Order_By>;
	created_at?: InputMaybe<Order_By>;
	domain?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	organization_id?: InputMaybe<Order_By>;
	updated_at?: InputMaybe<Order_By>;
	verification_method?: InputMaybe<Order_By>;
	verification_status?: InputMaybe<Order_By>;
	verification_token?: InputMaybe<Order_By>;
	verified_at?: InputMaybe<Order_By>;
};

/** aggregate min on columns */
export type Organization_Domains_Min_Fields = {
	__typename?: 'organization_domains_min_fields';
	auto_join_role?: Maybe<Scalars['String']['output']>;
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	domain?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	organization_id?: Maybe<Scalars['uuid']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
	verification_method?: Maybe<Scalars['String']['output']>;
	verification_status?: Maybe<Scalars['String']['output']>;
	verification_token?: Maybe<Scalars['String']['output']>;
	verified_at?: Maybe<Scalars['timestamptz']['output']>;
};

/** order by min() on columns of table "organization_domains" */
export type Organization_Domains_Min_Order_By = {
	auto_join_role?: InputMaybe<Order_By>;
	created_at?: InputMaybe<Order_By>;
	domain?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	organization_id?: InputMaybe<Order_By>;
	updated_at?: InputMaybe<Order_By>;
	verification_method?: InputMaybe<Order_By>;
	verification_status?: InputMaybe<Order_By>;
	verification_token?: InputMaybe<Order_By>;
	verified_at?: InputMaybe<Order_By>;
};

/** response of any mutation on the table "organization_domains" */
export type Organization_Domains_Mutation_Response = {
	__typename?: 'organization_domains_mutation_response';
	/** number of rows affected by the mutation */
	affected_rows: Scalars['Int']['output'];
	/** data from the rows affected by the mutation */
	returning: Array<Organization_Domains>;
};

/** on_conflict condition type for table "organization_domains" */
export type Organization_Domains_On_Conflict = {
	constraint: Organization_Domains_Constraint;
	update_columns?: Array<Organization_Domains_Update_Column>;
	where?: InputMaybe<Organization_Domains_Bool_Exp>;
};

/** Ordering options when selecting data from "organization_domains". */
export type Organization_Domains_Order_By = {
	auto_join_enabled?: InputMaybe<Order_By>;
	auto_join_role?: InputMaybe<Order_By>;
	created_at?: InputMaybe<Order_By>;
	domain?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	organization?: InputMaybe<Organizations_Order_By>;
	organization_id?: InputMaybe<Order_By>;
	updated_at?: InputMaybe<Order_By>;
	verification_method?: InputMaybe<Order_By>;
	verification_status?: InputMaybe<Order_By>;
	verification_token?: InputMaybe<Order_By>;
	verified_at?: InputMaybe<Order_By>;
};

/** primary key columns input for table: organization_domains */
export type Organization_Domains_Pk_Columns_Input = {
	id: Scalars['uuid']['input'];
};

/** select columns of table "organization_domains" */
export enum Organization_Domains_Select_Column {
	/** column name */
	AutoJoinEnabled = 'auto_join_enabled',
	/** column name */
	AutoJoinRole = 'auto_join_role',
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	Domain = 'domain',
	/** column name */
	Id = 'id',
	/** column name */
	OrganizationId = 'organization_id',
	/** column name */
	UpdatedAt = 'updated_at',
	/** column name */
	VerificationMethod = 'verification_method',
	/** column name */
	VerificationStatus = 'verification_status',
	/** column name */
	VerificationToken = 'verification_token',
	/** column name */
	VerifiedAt = 'verified_at'
}

/** select "organization_domains_aggregate_bool_exp_bool_and_arguments_columns" columns of table "organization_domains" */
export enum Organization_Domains_Select_Column_Organization_Domains_Aggregate_Bool_Exp_Bool_And_Arguments_Columns {
	/** column name */
	AutoJoinEnabled = 'auto_join_enabled'
}

/** select "organization_domains_aggregate_bool_exp_bool_or_arguments_columns" columns of table "organization_domains" */
export enum Organization_Domains_Select_Column_Organization_Domains_Aggregate_Bool_Exp_Bool_Or_Arguments_Columns {
	/** column name */
	AutoJoinEnabled = 'auto_join_enabled'
}

/** input type for updating data in table "organization_domains" */
export type Organization_Domains_Set_Input = {
	auto_join_enabled?: InputMaybe<Scalars['Boolean']['input']>;
	auto_join_role?: InputMaybe<Scalars['String']['input']>;
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	domain?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	organization_id?: InputMaybe<Scalars['uuid']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
	verification_method?: InputMaybe<Scalars['String']['input']>;
	verification_status?: InputMaybe<Scalars['String']['input']>;
	verification_token?: InputMaybe<Scalars['String']['input']>;
	verified_at?: InputMaybe<Scalars['timestamptz']['input']>;
};

/** Streaming cursor of the table "organization_domains" */
export type Organization_Domains_Stream_Cursor_Input = {
	/** Stream column input with initial value */
	initial_value: Organization_Domains_Stream_Cursor_Value_Input;
	/** cursor ordering */
	ordering?: InputMaybe<Cursor_Ordering>;
};

/** Initial value of the column from where the streaming should start */
export type Organization_Domains_Stream_Cursor_Value_Input = {
	auto_join_enabled?: InputMaybe<Scalars['Boolean']['input']>;
	auto_join_role?: InputMaybe<Scalars['String']['input']>;
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	domain?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	organization_id?: InputMaybe<Scalars['uuid']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
	verification_method?: InputMaybe<Scalars['String']['input']>;
	verification_status?: InputMaybe<Scalars['String']['input']>;
	verification_token?: InputMaybe<Scalars['String']['input']>;
	verified_at?: InputMaybe<Scalars['timestamptz']['input']>;
};

/** update columns of table "organization_domains" */
export enum Organization_Domains_Update_Column {
	/** column name */
	AutoJoinEnabled = 'auto_join_enabled',
	/** column name */
	AutoJoinRole = 'auto_join_role',
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	Domain = 'domain',
	/** column name */
	Id = 'id',
	/** column name */
	OrganizationId = 'organization_id',
	/** column name */
	UpdatedAt = 'updated_at',
	/** column name */
	VerificationMethod = 'verification_method',
	/** column name */
	VerificationStatus = 'verification_status',
	/** column name */
	VerificationToken = 'verification_token',
	/** column name */
	VerifiedAt = 'verified_at'
}

export type Organization_Domains_Updates = {
	/** sets the columns of the filtered rows to the given values */
	_set?: InputMaybe<Organization_Domains_Set_Input>;
	/** filter the rows which have to be updated */
	where: Organization_Domains_Bool_Exp;
};

/** columns and relationships of "organization_invitations" */
export type Organization_Invitations = {
	__typename?: 'organization_invitations';
	accepted_at?: Maybe<Scalars['timestamptz']['output']>;
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	email: Scalars['String']['output'];
	expires_at: Scalars['timestamptz']['output'];
	id: Scalars['uuid']['output'];
	invitation_token: Scalars['String']['output'];
	invited_by?: Maybe<Scalars['uuid']['output']>;
	/** An object relationship */
	invited_by_user?: Maybe<Users>;
	/** An object relationship */
	organization?: Maybe<Organizations>;
	organization_id?: Maybe<Scalars['uuid']['output']>;
	role: Scalars['String']['output'];
	status?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
	/** An object relationship */
	user?: Maybe<Users>;
};

/** aggregated selection of "organization_invitations" */
export type Organization_Invitations_Aggregate = {
	__typename?: 'organization_invitations_aggregate';
	aggregate?: Maybe<Organization_Invitations_Aggregate_Fields>;
	nodes: Array<Organization_Invitations>;
};

export type Organization_Invitations_Aggregate_Bool_Exp = {
	count?: InputMaybe<Organization_Invitations_Aggregate_Bool_Exp_Count>;
};

export type Organization_Invitations_Aggregate_Bool_Exp_Count = {
	arguments?: InputMaybe<Array<Organization_Invitations_Select_Column>>;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
	filter?: InputMaybe<Organization_Invitations_Bool_Exp>;
	predicate: Int_Comparison_Exp;
};

/** aggregate fields of "organization_invitations" */
export type Organization_Invitations_Aggregate_Fields = {
	__typename?: 'organization_invitations_aggregate_fields';
	count: Scalars['Int']['output'];
	max?: Maybe<Organization_Invitations_Max_Fields>;
	min?: Maybe<Organization_Invitations_Min_Fields>;
};

/** aggregate fields of "organization_invitations" */
export type Organization_Invitations_Aggregate_FieldsCountArgs = {
	columns?: InputMaybe<Array<Organization_Invitations_Select_Column>>;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
};

/** order by aggregate values of table "organization_invitations" */
export type Organization_Invitations_Aggregate_Order_By = {
	count?: InputMaybe<Order_By>;
	max?: InputMaybe<Organization_Invitations_Max_Order_By>;
	min?: InputMaybe<Organization_Invitations_Min_Order_By>;
};

/** input type for inserting array relation for remote table "organization_invitations" */
export type Organization_Invitations_Arr_Rel_Insert_Input = {
	data: Array<Organization_Invitations_Insert_Input>;
	/** upsert condition */
	on_conflict?: InputMaybe<Organization_Invitations_On_Conflict>;
};

/** Boolean expression to filter rows from the table "organization_invitations". All fields are combined with a logical 'AND'. */
export type Organization_Invitations_Bool_Exp = {
	_and?: InputMaybe<Array<Organization_Invitations_Bool_Exp>>;
	_not?: InputMaybe<Organization_Invitations_Bool_Exp>;
	_or?: InputMaybe<Array<Organization_Invitations_Bool_Exp>>;
	accepted_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	created_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	email?: InputMaybe<String_Comparison_Exp>;
	expires_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	id?: InputMaybe<Uuid_Comparison_Exp>;
	invitation_token?: InputMaybe<String_Comparison_Exp>;
	invited_by?: InputMaybe<Uuid_Comparison_Exp>;
	invited_by_user?: InputMaybe<Users_Bool_Exp>;
	organization?: InputMaybe<Organizations_Bool_Exp>;
	organization_id?: InputMaybe<Uuid_Comparison_Exp>;
	role?: InputMaybe<String_Comparison_Exp>;
	status?: InputMaybe<String_Comparison_Exp>;
	updated_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	user?: InputMaybe<Users_Bool_Exp>;
};

/** unique or primary key constraints on table "organization_invitations" */
export enum Organization_Invitations_Constraint {
	/** unique or primary key constraint on columns "invitation_token" */
	OrganizationInvitationsInvitationTokenKey = 'organization_invitations_invitation_token_key',
	/** unique or primary key constraint on columns "id" */
	OrganizationInvitationsPkey = 'organization_invitations_pkey'
}

/** input type for inserting data into table "organization_invitations" */
export type Organization_Invitations_Insert_Input = {
	accepted_at?: InputMaybe<Scalars['timestamptz']['input']>;
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	email?: InputMaybe<Scalars['String']['input']>;
	expires_at?: InputMaybe<Scalars['timestamptz']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	invitation_token?: InputMaybe<Scalars['String']['input']>;
	invited_by?: InputMaybe<Scalars['uuid']['input']>;
	invited_by_user?: InputMaybe<Users_Obj_Rel_Insert_Input>;
	organization?: InputMaybe<Organizations_Obj_Rel_Insert_Input>;
	organization_id?: InputMaybe<Scalars['uuid']['input']>;
	role?: InputMaybe<Scalars['String']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
	user?: InputMaybe<Users_Obj_Rel_Insert_Input>;
};

/** aggregate max on columns */
export type Organization_Invitations_Max_Fields = {
	__typename?: 'organization_invitations_max_fields';
	accepted_at?: Maybe<Scalars['timestamptz']['output']>;
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	email?: Maybe<Scalars['String']['output']>;
	expires_at?: Maybe<Scalars['timestamptz']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	invitation_token?: Maybe<Scalars['String']['output']>;
	invited_by?: Maybe<Scalars['uuid']['output']>;
	organization_id?: Maybe<Scalars['uuid']['output']>;
	role?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
};

/** order by max() on columns of table "organization_invitations" */
export type Organization_Invitations_Max_Order_By = {
	accepted_at?: InputMaybe<Order_By>;
	created_at?: InputMaybe<Order_By>;
	email?: InputMaybe<Order_By>;
	expires_at?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	invitation_token?: InputMaybe<Order_By>;
	invited_by?: InputMaybe<Order_By>;
	organization_id?: InputMaybe<Order_By>;
	role?: InputMaybe<Order_By>;
	status?: InputMaybe<Order_By>;
	updated_at?: InputMaybe<Order_By>;
};

/** aggregate min on columns */
export type Organization_Invitations_Min_Fields = {
	__typename?: 'organization_invitations_min_fields';
	accepted_at?: Maybe<Scalars['timestamptz']['output']>;
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	email?: Maybe<Scalars['String']['output']>;
	expires_at?: Maybe<Scalars['timestamptz']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	invitation_token?: Maybe<Scalars['String']['output']>;
	invited_by?: Maybe<Scalars['uuid']['output']>;
	organization_id?: Maybe<Scalars['uuid']['output']>;
	role?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
};

/** order by min() on columns of table "organization_invitations" */
export type Organization_Invitations_Min_Order_By = {
	accepted_at?: InputMaybe<Order_By>;
	created_at?: InputMaybe<Order_By>;
	email?: InputMaybe<Order_By>;
	expires_at?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	invitation_token?: InputMaybe<Order_By>;
	invited_by?: InputMaybe<Order_By>;
	organization_id?: InputMaybe<Order_By>;
	role?: InputMaybe<Order_By>;
	status?: InputMaybe<Order_By>;
	updated_at?: InputMaybe<Order_By>;
};

/** response of any mutation on the table "organization_invitations" */
export type Organization_Invitations_Mutation_Response = {
	__typename?: 'organization_invitations_mutation_response';
	/** number of rows affected by the mutation */
	affected_rows: Scalars['Int']['output'];
	/** data from the rows affected by the mutation */
	returning: Array<Organization_Invitations>;
};

/** on_conflict condition type for table "organization_invitations" */
export type Organization_Invitations_On_Conflict = {
	constraint: Organization_Invitations_Constraint;
	update_columns?: Array<Organization_Invitations_Update_Column>;
	where?: InputMaybe<Organization_Invitations_Bool_Exp>;
};

/** Ordering options when selecting data from "organization_invitations". */
export type Organization_Invitations_Order_By = {
	accepted_at?: InputMaybe<Order_By>;
	created_at?: InputMaybe<Order_By>;
	email?: InputMaybe<Order_By>;
	expires_at?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	invitation_token?: InputMaybe<Order_By>;
	invited_by?: InputMaybe<Order_By>;
	invited_by_user?: InputMaybe<Users_Order_By>;
	organization?: InputMaybe<Organizations_Order_By>;
	organization_id?: InputMaybe<Order_By>;
	role?: InputMaybe<Order_By>;
	status?: InputMaybe<Order_By>;
	updated_at?: InputMaybe<Order_By>;
	user?: InputMaybe<Users_Order_By>;
};

/** primary key columns input for table: organization_invitations */
export type Organization_Invitations_Pk_Columns_Input = {
	id: Scalars['uuid']['input'];
};

/** select columns of table "organization_invitations" */
export enum Organization_Invitations_Select_Column {
	/** column name */
	AcceptedAt = 'accepted_at',
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	Email = 'email',
	/** column name */
	ExpiresAt = 'expires_at',
	/** column name */
	Id = 'id',
	/** column name */
	InvitationToken = 'invitation_token',
	/** column name */
	InvitedBy = 'invited_by',
	/** column name */
	OrganizationId = 'organization_id',
	/** column name */
	Role = 'role',
	/** column name */
	Status = 'status',
	/** column name */
	UpdatedAt = 'updated_at'
}

/** input type for updating data in table "organization_invitations" */
export type Organization_Invitations_Set_Input = {
	accepted_at?: InputMaybe<Scalars['timestamptz']['input']>;
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	email?: InputMaybe<Scalars['String']['input']>;
	expires_at?: InputMaybe<Scalars['timestamptz']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	invitation_token?: InputMaybe<Scalars['String']['input']>;
	invited_by?: InputMaybe<Scalars['uuid']['input']>;
	organization_id?: InputMaybe<Scalars['uuid']['input']>;
	role?: InputMaybe<Scalars['String']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
};

/** Streaming cursor of the table "organization_invitations" */
export type Organization_Invitations_Stream_Cursor_Input = {
	/** Stream column input with initial value */
	initial_value: Organization_Invitations_Stream_Cursor_Value_Input;
	/** cursor ordering */
	ordering?: InputMaybe<Cursor_Ordering>;
};

/** Initial value of the column from where the streaming should start */
export type Organization_Invitations_Stream_Cursor_Value_Input = {
	accepted_at?: InputMaybe<Scalars['timestamptz']['input']>;
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	email?: InputMaybe<Scalars['String']['input']>;
	expires_at?: InputMaybe<Scalars['timestamptz']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	invitation_token?: InputMaybe<Scalars['String']['input']>;
	invited_by?: InputMaybe<Scalars['uuid']['input']>;
	organization_id?: InputMaybe<Scalars['uuid']['input']>;
	role?: InputMaybe<Scalars['String']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
};

/** update columns of table "organization_invitations" */
export enum Organization_Invitations_Update_Column {
	/** column name */
	AcceptedAt = 'accepted_at',
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	Email = 'email',
	/** column name */
	ExpiresAt = 'expires_at',
	/** column name */
	Id = 'id',
	/** column name */
	InvitationToken = 'invitation_token',
	/** column name */
	InvitedBy = 'invited_by',
	/** column name */
	OrganizationId = 'organization_id',
	/** column name */
	Role = 'role',
	/** column name */
	Status = 'status',
	/** column name */
	UpdatedAt = 'updated_at'
}

export type Organization_Invitations_Updates = {
	/** sets the columns of the filtered rows to the given values */
	_set?: InputMaybe<Organization_Invitations_Set_Input>;
	/** filter the rows which have to be updated */
	where: Organization_Invitations_Bool_Exp;
};

/** columns and relationships of "organizations" */
export type Organizations = {
	__typename?: 'organizations';
	/** An array relationship */
	auth_audit_logs: Array<Auth_Audit_Logs>;
	/** An aggregate relationship */
	auth_audit_logs_aggregate: Auth_Audit_Logs_Aggregate;
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	email_domain?: Maybe<Scalars['String']['output']>;
	id: Scalars['uuid']['output'];
	is_active?: Maybe<Scalars['Boolean']['output']>;
	logto_org_id: Scalars['uuid']['output'];
	name: Scalars['String']['output'];
	/** An array relationship */
	organization_domains: Array<Organization_Domains>;
	/** An aggregate relationship */
	organization_domains_aggregate: Organization_Domains_Aggregate;
	/** An array relationship */
	organization_invitations: Array<Organization_Invitations>;
	/** An aggregate relationship */
	organization_invitations_aggregate: Organization_Invitations_Aggregate;
	organization_type?: Maybe<Scalars['String']['output']>;
	settings?: Maybe<Scalars['jsonb']['output']>;
	slug: Scalars['String']['output'];
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
	/** An array relationship */
	user_organizations: Array<User_Organizations>;
	/** An aggregate relationship */
	user_organizations_aggregate: User_Organizations_Aggregate;
	website_url?: Maybe<Scalars['String']['output']>;
};

/** columns and relationships of "organizations" */
export type OrganizationsAuth_Audit_LogsArgs = {
	distinct_on?: InputMaybe<Array<Auth_Audit_Logs_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Auth_Audit_Logs_Order_By>>;
	where?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
};

/** columns and relationships of "organizations" */
export type OrganizationsAuth_Audit_Logs_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Auth_Audit_Logs_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Auth_Audit_Logs_Order_By>>;
	where?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
};

/** columns and relationships of "organizations" */
export type OrganizationsOrganization_DomainsArgs = {
	distinct_on?: InputMaybe<Array<Organization_Domains_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Domains_Order_By>>;
	where?: InputMaybe<Organization_Domains_Bool_Exp>;
};

/** columns and relationships of "organizations" */
export type OrganizationsOrganization_Domains_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Organization_Domains_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Domains_Order_By>>;
	where?: InputMaybe<Organization_Domains_Bool_Exp>;
};

/** columns and relationships of "organizations" */
export type OrganizationsOrganization_InvitationsArgs = {
	distinct_on?: InputMaybe<Array<Organization_Invitations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Invitations_Order_By>>;
	where?: InputMaybe<Organization_Invitations_Bool_Exp>;
};

/** columns and relationships of "organizations" */
export type OrganizationsOrganization_Invitations_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Organization_Invitations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Invitations_Order_By>>;
	where?: InputMaybe<Organization_Invitations_Bool_Exp>;
};

/** columns and relationships of "organizations" */
export type OrganizationsSettingsArgs = {
	path?: InputMaybe<Scalars['String']['input']>;
};

/** columns and relationships of "organizations" */
export type OrganizationsUser_OrganizationsArgs = {
	distinct_on?: InputMaybe<Array<User_Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<User_Organizations_Order_By>>;
	where?: InputMaybe<User_Organizations_Bool_Exp>;
};

/** columns and relationships of "organizations" */
export type OrganizationsUser_Organizations_AggregateArgs = {
	distinct_on?: InputMaybe<Array<User_Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<User_Organizations_Order_By>>;
	where?: InputMaybe<User_Organizations_Bool_Exp>;
};

/** aggregated selection of "organizations" */
export type Organizations_Aggregate = {
	__typename?: 'organizations_aggregate';
	aggregate?: Maybe<Organizations_Aggregate_Fields>;
	nodes: Array<Organizations>;
};

/** aggregate fields of "organizations" */
export type Organizations_Aggregate_Fields = {
	__typename?: 'organizations_aggregate_fields';
	count: Scalars['Int']['output'];
	max?: Maybe<Organizations_Max_Fields>;
	min?: Maybe<Organizations_Min_Fields>;
};

/** aggregate fields of "organizations" */
export type Organizations_Aggregate_FieldsCountArgs = {
	columns?: InputMaybe<Array<Organizations_Select_Column>>;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
};

/** append existing jsonb value of filtered columns with new jsonb value */
export type Organizations_Append_Input = {
	settings?: InputMaybe<Scalars['jsonb']['input']>;
};

/** Boolean expression to filter rows from the table "organizations". All fields are combined with a logical 'AND'. */
export type Organizations_Bool_Exp = {
	_and?: InputMaybe<Array<Organizations_Bool_Exp>>;
	_not?: InputMaybe<Organizations_Bool_Exp>;
	_or?: InputMaybe<Array<Organizations_Bool_Exp>>;
	auth_audit_logs?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
	auth_audit_logs_aggregate?: InputMaybe<Auth_Audit_Logs_Aggregate_Bool_Exp>;
	created_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	description?: InputMaybe<String_Comparison_Exp>;
	email_domain?: InputMaybe<String_Comparison_Exp>;
	id?: InputMaybe<Uuid_Comparison_Exp>;
	is_active?: InputMaybe<Boolean_Comparison_Exp>;
	logto_org_id?: InputMaybe<Uuid_Comparison_Exp>;
	name?: InputMaybe<String_Comparison_Exp>;
	organization_domains?: InputMaybe<Organization_Domains_Bool_Exp>;
	organization_domains_aggregate?: InputMaybe<Organization_Domains_Aggregate_Bool_Exp>;
	organization_invitations?: InputMaybe<Organization_Invitations_Bool_Exp>;
	organization_invitations_aggregate?: InputMaybe<Organization_Invitations_Aggregate_Bool_Exp>;
	organization_type?: InputMaybe<String_Comparison_Exp>;
	settings?: InputMaybe<Jsonb_Comparison_Exp>;
	slug?: InputMaybe<String_Comparison_Exp>;
	updated_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	user_organizations?: InputMaybe<User_Organizations_Bool_Exp>;
	user_organizations_aggregate?: InputMaybe<User_Organizations_Aggregate_Bool_Exp>;
	website_url?: InputMaybe<String_Comparison_Exp>;
};

/** unique or primary key constraints on table "organizations" */
export enum Organizations_Constraint {
	/** unique or primary key constraint on columns "logto_org_id" */
	OrganizationsLogtoOrgIdKey = 'organizations_logto_org_id_key',
	/** unique or primary key constraint on columns "id" */
	OrganizationsPkey = 'organizations_pkey',
	/** unique or primary key constraint on columns "slug" */
	OrganizationsSlugKey = 'organizations_slug_key'
}

/** delete the field or element with specified path (for JSON arrays, negative integers count from the end) */
export type Organizations_Delete_At_Path_Input = {
	settings?: InputMaybe<Array<Scalars['String']['input']>>;
};

/** delete the array element with specified index (negative integers count from the end). throws an error if top level container is not an array */
export type Organizations_Delete_Elem_Input = {
	settings?: InputMaybe<Scalars['Int']['input']>;
};

/** delete key/value pair or string element. key/value pairs are matched based on their key value */
export type Organizations_Delete_Key_Input = {
	settings?: InputMaybe<Scalars['String']['input']>;
};

/** input type for inserting data into table "organizations" */
export type Organizations_Insert_Input = {
	auth_audit_logs?: InputMaybe<Auth_Audit_Logs_Arr_Rel_Insert_Input>;
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	description?: InputMaybe<Scalars['String']['input']>;
	email_domain?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	is_active?: InputMaybe<Scalars['Boolean']['input']>;
	logto_org_id?: InputMaybe<Scalars['uuid']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	organization_domains?: InputMaybe<Organization_Domains_Arr_Rel_Insert_Input>;
	organization_invitations?: InputMaybe<Organization_Invitations_Arr_Rel_Insert_Input>;
	organization_type?: InputMaybe<Scalars['String']['input']>;
	settings?: InputMaybe<Scalars['jsonb']['input']>;
	slug?: InputMaybe<Scalars['String']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
	user_organizations?: InputMaybe<User_Organizations_Arr_Rel_Insert_Input>;
	website_url?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate max on columns */
export type Organizations_Max_Fields = {
	__typename?: 'organizations_max_fields';
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	email_domain?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	logto_org_id?: Maybe<Scalars['uuid']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	organization_type?: Maybe<Scalars['String']['output']>;
	slug?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
	website_url?: Maybe<Scalars['String']['output']>;
};

/** aggregate min on columns */
export type Organizations_Min_Fields = {
	__typename?: 'organizations_min_fields';
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	description?: Maybe<Scalars['String']['output']>;
	email_domain?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	logto_org_id?: Maybe<Scalars['uuid']['output']>;
	name?: Maybe<Scalars['String']['output']>;
	organization_type?: Maybe<Scalars['String']['output']>;
	slug?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
	website_url?: Maybe<Scalars['String']['output']>;
};

/** response of any mutation on the table "organizations" */
export type Organizations_Mutation_Response = {
	__typename?: 'organizations_mutation_response';
	/** number of rows affected by the mutation */
	affected_rows: Scalars['Int']['output'];
	/** data from the rows affected by the mutation */
	returning: Array<Organizations>;
};

/** input type for inserting object relation for remote table "organizations" */
export type Organizations_Obj_Rel_Insert_Input = {
	data: Organizations_Insert_Input;
	/** upsert condition */
	on_conflict?: InputMaybe<Organizations_On_Conflict>;
};

/** on_conflict condition type for table "organizations" */
export type Organizations_On_Conflict = {
	constraint: Organizations_Constraint;
	update_columns?: Array<Organizations_Update_Column>;
	where?: InputMaybe<Organizations_Bool_Exp>;
};

/** Ordering options when selecting data from "organizations". */
export type Organizations_Order_By = {
	auth_audit_logs_aggregate?: InputMaybe<Auth_Audit_Logs_Aggregate_Order_By>;
	created_at?: InputMaybe<Order_By>;
	description?: InputMaybe<Order_By>;
	email_domain?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	is_active?: InputMaybe<Order_By>;
	logto_org_id?: InputMaybe<Order_By>;
	name?: InputMaybe<Order_By>;
	organization_domains_aggregate?: InputMaybe<Organization_Domains_Aggregate_Order_By>;
	organization_invitations_aggregate?: InputMaybe<Organization_Invitations_Aggregate_Order_By>;
	organization_type?: InputMaybe<Order_By>;
	settings?: InputMaybe<Order_By>;
	slug?: InputMaybe<Order_By>;
	updated_at?: InputMaybe<Order_By>;
	user_organizations_aggregate?: InputMaybe<User_Organizations_Aggregate_Order_By>;
	website_url?: InputMaybe<Order_By>;
};

/** primary key columns input for table: organizations */
export type Organizations_Pk_Columns_Input = {
	id: Scalars['uuid']['input'];
};

/** prepend existing jsonb value of filtered columns with new jsonb value */
export type Organizations_Prepend_Input = {
	settings?: InputMaybe<Scalars['jsonb']['input']>;
};

/** select columns of table "organizations" */
export enum Organizations_Select_Column {
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	Description = 'description',
	/** column name */
	EmailDomain = 'email_domain',
	/** column name */
	Id = 'id',
	/** column name */
	IsActive = 'is_active',
	/** column name */
	LogtoOrgId = 'logto_org_id',
	/** column name */
	Name = 'name',
	/** column name */
	OrganizationType = 'organization_type',
	/** column name */
	Settings = 'settings',
	/** column name */
	Slug = 'slug',
	/** column name */
	UpdatedAt = 'updated_at',
	/** column name */
	WebsiteUrl = 'website_url'
}

/** input type for updating data in table "organizations" */
export type Organizations_Set_Input = {
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	description?: InputMaybe<Scalars['String']['input']>;
	email_domain?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	is_active?: InputMaybe<Scalars['Boolean']['input']>;
	logto_org_id?: InputMaybe<Scalars['uuid']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	organization_type?: InputMaybe<Scalars['String']['input']>;
	settings?: InputMaybe<Scalars['jsonb']['input']>;
	slug?: InputMaybe<Scalars['String']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
	website_url?: InputMaybe<Scalars['String']['input']>;
};

/** Streaming cursor of the table "organizations" */
export type Organizations_Stream_Cursor_Input = {
	/** Stream column input with initial value */
	initial_value: Organizations_Stream_Cursor_Value_Input;
	/** cursor ordering */
	ordering?: InputMaybe<Cursor_Ordering>;
};

/** Initial value of the column from where the streaming should start */
export type Organizations_Stream_Cursor_Value_Input = {
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	description?: InputMaybe<Scalars['String']['input']>;
	email_domain?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	is_active?: InputMaybe<Scalars['Boolean']['input']>;
	logto_org_id?: InputMaybe<Scalars['uuid']['input']>;
	name?: InputMaybe<Scalars['String']['input']>;
	organization_type?: InputMaybe<Scalars['String']['input']>;
	settings?: InputMaybe<Scalars['jsonb']['input']>;
	slug?: InputMaybe<Scalars['String']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
	website_url?: InputMaybe<Scalars['String']['input']>;
};

/** update columns of table "organizations" */
export enum Organizations_Update_Column {
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	Description = 'description',
	/** column name */
	EmailDomain = 'email_domain',
	/** column name */
	Id = 'id',
	/** column name */
	IsActive = 'is_active',
	/** column name */
	LogtoOrgId = 'logto_org_id',
	/** column name */
	Name = 'name',
	/** column name */
	OrganizationType = 'organization_type',
	/** column name */
	Settings = 'settings',
	/** column name */
	Slug = 'slug',
	/** column name */
	UpdatedAt = 'updated_at',
	/** column name */
	WebsiteUrl = 'website_url'
}

export type Organizations_Updates = {
	/** append existing jsonb value of filtered columns with new jsonb value */
	_append?: InputMaybe<Organizations_Append_Input>;
	/** delete the field or element with specified path (for JSON arrays, negative integers count from the end) */
	_delete_at_path?: InputMaybe<Organizations_Delete_At_Path_Input>;
	/** delete the array element with specified index (negative integers count from the end). throws an error if top level container is not an array */
	_delete_elem?: InputMaybe<Organizations_Delete_Elem_Input>;
	/** delete key/value pair or string element. key/value pairs are matched based on their key value */
	_delete_key?: InputMaybe<Organizations_Delete_Key_Input>;
	/** prepend existing jsonb value of filtered columns with new jsonb value */
	_prepend?: InputMaybe<Organizations_Prepend_Input>;
	/** sets the columns of the filtered rows to the given values */
	_set?: InputMaybe<Organizations_Set_Input>;
	/** filter the rows which have to be updated */
	where: Organizations_Bool_Exp;
};

export type Query_Root = {
	__typename?: 'query_root';
	/** An array relationship */
	auth_audit_logs: Array<Auth_Audit_Logs>;
	/** An aggregate relationship */
	auth_audit_logs_aggregate: Auth_Audit_Logs_Aggregate;
	/** fetch data from the table: "auth_audit_logs" using primary key columns */
	auth_audit_logs_by_pk?: Maybe<Auth_Audit_Logs>;
	/** An array relationship */
	organization_domains: Array<Organization_Domains>;
	/** An aggregate relationship */
	organization_domains_aggregate: Organization_Domains_Aggregate;
	/** fetch data from the table: "organization_domains" using primary key columns */
	organization_domains_by_pk?: Maybe<Organization_Domains>;
	/** An array relationship */
	organization_invitations: Array<Organization_Invitations>;
	/** An aggregate relationship */
	organization_invitations_aggregate: Organization_Invitations_Aggregate;
	/** fetch data from the table: "organization_invitations" using primary key columns */
	organization_invitations_by_pk?: Maybe<Organization_Invitations>;
	/** fetch data from the table: "organizations" */
	organizations: Array<Organizations>;
	/** fetch aggregated fields from the table: "organizations" */
	organizations_aggregate: Organizations_Aggregate;
	/** fetch data from the table: "organizations" using primary key columns */
	organizations_by_pk?: Maybe<Organizations>;
	/** fetch data from the table: "schema_migrations" */
	schema_migrations: Array<Schema_Migrations>;
	/** fetch aggregated fields from the table: "schema_migrations" */
	schema_migrations_aggregate: Schema_Migrations_Aggregate;
	/** fetch data from the table: "schema_migrations" using primary key columns */
	schema_migrations_by_pk?: Maybe<Schema_Migrations>;
	/** An array relationship */
	user_organizations: Array<User_Organizations>;
	/** An aggregate relationship */
	user_organizations_aggregate: User_Organizations_Aggregate;
	/** fetch data from the table: "user_organizations" using primary key columns */
	user_organizations_by_pk?: Maybe<User_Organizations>;
	/** fetch data from the table: "users" */
	users: Array<Users>;
	/** fetch aggregated fields from the table: "users" */
	users_aggregate: Users_Aggregate;
	/** fetch data from the table: "users" using primary key columns */
	users_by_pk?: Maybe<Users>;
	/** fetch data from the table: "webhook_logs" */
	webhook_logs: Array<Webhook_Logs>;
	/** fetch aggregated fields from the table: "webhook_logs" */
	webhook_logs_aggregate: Webhook_Logs_Aggregate;
	/** fetch data from the table: "webhook_logs" using primary key columns */
	webhook_logs_by_pk?: Maybe<Webhook_Logs>;
};

export type Query_RootAuth_Audit_LogsArgs = {
	distinct_on?: InputMaybe<Array<Auth_Audit_Logs_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Auth_Audit_Logs_Order_By>>;
	where?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
};

export type Query_RootAuth_Audit_Logs_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Auth_Audit_Logs_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Auth_Audit_Logs_Order_By>>;
	where?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
};

export type Query_RootAuth_Audit_Logs_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

export type Query_RootOrganization_DomainsArgs = {
	distinct_on?: InputMaybe<Array<Organization_Domains_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Domains_Order_By>>;
	where?: InputMaybe<Organization_Domains_Bool_Exp>;
};

export type Query_RootOrganization_Domains_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Organization_Domains_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Domains_Order_By>>;
	where?: InputMaybe<Organization_Domains_Bool_Exp>;
};

export type Query_RootOrganization_Domains_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

export type Query_RootOrganization_InvitationsArgs = {
	distinct_on?: InputMaybe<Array<Organization_Invitations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Invitations_Order_By>>;
	where?: InputMaybe<Organization_Invitations_Bool_Exp>;
};

export type Query_RootOrganization_Invitations_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Organization_Invitations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Invitations_Order_By>>;
	where?: InputMaybe<Organization_Invitations_Bool_Exp>;
};

export type Query_RootOrganization_Invitations_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

export type Query_RootOrganizationsArgs = {
	distinct_on?: InputMaybe<Array<Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organizations_Order_By>>;
	where?: InputMaybe<Organizations_Bool_Exp>;
};

export type Query_RootOrganizations_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organizations_Order_By>>;
	where?: InputMaybe<Organizations_Bool_Exp>;
};

export type Query_RootOrganizations_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

export type Query_RootSchema_MigrationsArgs = {
	distinct_on?: InputMaybe<Array<Schema_Migrations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Schema_Migrations_Order_By>>;
	where?: InputMaybe<Schema_Migrations_Bool_Exp>;
};

export type Query_RootSchema_Migrations_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Schema_Migrations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Schema_Migrations_Order_By>>;
	where?: InputMaybe<Schema_Migrations_Bool_Exp>;
};

export type Query_RootSchema_Migrations_By_PkArgs = {
	id: Scalars['Int']['input'];
};

export type Query_RootUser_OrganizationsArgs = {
	distinct_on?: InputMaybe<Array<User_Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<User_Organizations_Order_By>>;
	where?: InputMaybe<User_Organizations_Bool_Exp>;
};

export type Query_RootUser_Organizations_AggregateArgs = {
	distinct_on?: InputMaybe<Array<User_Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<User_Organizations_Order_By>>;
	where?: InputMaybe<User_Organizations_Bool_Exp>;
};

export type Query_RootUser_Organizations_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

export type Query_RootUsersArgs = {
	distinct_on?: InputMaybe<Array<Users_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Users_Order_By>>;
	where?: InputMaybe<Users_Bool_Exp>;
};

export type Query_RootUsers_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Users_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Users_Order_By>>;
	where?: InputMaybe<Users_Bool_Exp>;
};

export type Query_RootUsers_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

export type Query_RootWebhook_LogsArgs = {
	distinct_on?: InputMaybe<Array<Webhook_Logs_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Webhook_Logs_Order_By>>;
	where?: InputMaybe<Webhook_Logs_Bool_Exp>;
};

export type Query_RootWebhook_Logs_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Webhook_Logs_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Webhook_Logs_Order_By>>;
	where?: InputMaybe<Webhook_Logs_Bool_Exp>;
};

export type Query_RootWebhook_Logs_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

/** columns and relationships of "schema_migrations" */
export type Schema_Migrations = {
	__typename?: 'schema_migrations';
	applied_at?: Maybe<Scalars['timestamptz']['output']>;
	id: Scalars['Int']['output'];
	version: Scalars['String']['output'];
};

/** aggregated selection of "schema_migrations" */
export type Schema_Migrations_Aggregate = {
	__typename?: 'schema_migrations_aggregate';
	aggregate?: Maybe<Schema_Migrations_Aggregate_Fields>;
	nodes: Array<Schema_Migrations>;
};

/** aggregate fields of "schema_migrations" */
export type Schema_Migrations_Aggregate_Fields = {
	__typename?: 'schema_migrations_aggregate_fields';
	avg?: Maybe<Schema_Migrations_Avg_Fields>;
	count: Scalars['Int']['output'];
	max?: Maybe<Schema_Migrations_Max_Fields>;
	min?: Maybe<Schema_Migrations_Min_Fields>;
	stddev?: Maybe<Schema_Migrations_Stddev_Fields>;
	stddev_pop?: Maybe<Schema_Migrations_Stddev_Pop_Fields>;
	stddev_samp?: Maybe<Schema_Migrations_Stddev_Samp_Fields>;
	sum?: Maybe<Schema_Migrations_Sum_Fields>;
	var_pop?: Maybe<Schema_Migrations_Var_Pop_Fields>;
	var_samp?: Maybe<Schema_Migrations_Var_Samp_Fields>;
	variance?: Maybe<Schema_Migrations_Variance_Fields>;
};

/** aggregate fields of "schema_migrations" */
export type Schema_Migrations_Aggregate_FieldsCountArgs = {
	columns?: InputMaybe<Array<Schema_Migrations_Select_Column>>;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
};

/** aggregate avg on columns */
export type Schema_Migrations_Avg_Fields = {
	__typename?: 'schema_migrations_avg_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

/** Boolean expression to filter rows from the table "schema_migrations". All fields are combined with a logical 'AND'. */
export type Schema_Migrations_Bool_Exp = {
	_and?: InputMaybe<Array<Schema_Migrations_Bool_Exp>>;
	_not?: InputMaybe<Schema_Migrations_Bool_Exp>;
	_or?: InputMaybe<Array<Schema_Migrations_Bool_Exp>>;
	applied_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	id?: InputMaybe<Int_Comparison_Exp>;
	version?: InputMaybe<String_Comparison_Exp>;
};

/** unique or primary key constraints on table "schema_migrations" */
export enum Schema_Migrations_Constraint {
	/** unique or primary key constraint on columns "id" */
	SchemaMigrationsPkey = 'schema_migrations_pkey',
	/** unique or primary key constraint on columns "version" */
	SchemaMigrationsVersionKey = 'schema_migrations_version_key'
}

/** input type for incrementing numeric columns in table "schema_migrations" */
export type Schema_Migrations_Inc_Input = {
	id?: InputMaybe<Scalars['Int']['input']>;
};

/** input type for inserting data into table "schema_migrations" */
export type Schema_Migrations_Insert_Input = {
	applied_at?: InputMaybe<Scalars['timestamptz']['input']>;
	id?: InputMaybe<Scalars['Int']['input']>;
	version?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate max on columns */
export type Schema_Migrations_Max_Fields = {
	__typename?: 'schema_migrations_max_fields';
	applied_at?: Maybe<Scalars['timestamptz']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	version?: Maybe<Scalars['String']['output']>;
};

/** aggregate min on columns */
export type Schema_Migrations_Min_Fields = {
	__typename?: 'schema_migrations_min_fields';
	applied_at?: Maybe<Scalars['timestamptz']['output']>;
	id?: Maybe<Scalars['Int']['output']>;
	version?: Maybe<Scalars['String']['output']>;
};

/** response of any mutation on the table "schema_migrations" */
export type Schema_Migrations_Mutation_Response = {
	__typename?: 'schema_migrations_mutation_response';
	/** number of rows affected by the mutation */
	affected_rows: Scalars['Int']['output'];
	/** data from the rows affected by the mutation */
	returning: Array<Schema_Migrations>;
};

/** on_conflict condition type for table "schema_migrations" */
export type Schema_Migrations_On_Conflict = {
	constraint: Schema_Migrations_Constraint;
	update_columns?: Array<Schema_Migrations_Update_Column>;
	where?: InputMaybe<Schema_Migrations_Bool_Exp>;
};

/** Ordering options when selecting data from "schema_migrations". */
export type Schema_Migrations_Order_By = {
	applied_at?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	version?: InputMaybe<Order_By>;
};

/** primary key columns input for table: schema_migrations */
export type Schema_Migrations_Pk_Columns_Input = {
	id: Scalars['Int']['input'];
};

/** select columns of table "schema_migrations" */
export enum Schema_Migrations_Select_Column {
	/** column name */
	AppliedAt = 'applied_at',
	/** column name */
	Id = 'id',
	/** column name */
	Version = 'version'
}

/** input type for updating data in table "schema_migrations" */
export type Schema_Migrations_Set_Input = {
	applied_at?: InputMaybe<Scalars['timestamptz']['input']>;
	id?: InputMaybe<Scalars['Int']['input']>;
	version?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate stddev on columns */
export type Schema_Migrations_Stddev_Fields = {
	__typename?: 'schema_migrations_stddev_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_pop on columns */
export type Schema_Migrations_Stddev_Pop_Fields = {
	__typename?: 'schema_migrations_stddev_pop_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_samp on columns */
export type Schema_Migrations_Stddev_Samp_Fields = {
	__typename?: 'schema_migrations_stddev_samp_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

/** Streaming cursor of the table "schema_migrations" */
export type Schema_Migrations_Stream_Cursor_Input = {
	/** Stream column input with initial value */
	initial_value: Schema_Migrations_Stream_Cursor_Value_Input;
	/** cursor ordering */
	ordering?: InputMaybe<Cursor_Ordering>;
};

/** Initial value of the column from where the streaming should start */
export type Schema_Migrations_Stream_Cursor_Value_Input = {
	applied_at?: InputMaybe<Scalars['timestamptz']['input']>;
	id?: InputMaybe<Scalars['Int']['input']>;
	version?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate sum on columns */
export type Schema_Migrations_Sum_Fields = {
	__typename?: 'schema_migrations_sum_fields';
	id?: Maybe<Scalars['Int']['output']>;
};

/** update columns of table "schema_migrations" */
export enum Schema_Migrations_Update_Column {
	/** column name */
	AppliedAt = 'applied_at',
	/** column name */
	Id = 'id',
	/** column name */
	Version = 'version'
}

export type Schema_Migrations_Updates = {
	/** increments the numeric columns with given value of the filtered values */
	_inc?: InputMaybe<Schema_Migrations_Inc_Input>;
	/** sets the columns of the filtered rows to the given values */
	_set?: InputMaybe<Schema_Migrations_Set_Input>;
	/** filter the rows which have to be updated */
	where: Schema_Migrations_Bool_Exp;
};

/** aggregate var_pop on columns */
export type Schema_Migrations_Var_Pop_Fields = {
	__typename?: 'schema_migrations_var_pop_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate var_samp on columns */
export type Schema_Migrations_Var_Samp_Fields = {
	__typename?: 'schema_migrations_var_samp_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate variance on columns */
export type Schema_Migrations_Variance_Fields = {
	__typename?: 'schema_migrations_variance_fields';
	id?: Maybe<Scalars['Float']['output']>;
};

export type Subscription_Root = {
	__typename?: 'subscription_root';
	/** An array relationship */
	auth_audit_logs: Array<Auth_Audit_Logs>;
	/** An aggregate relationship */
	auth_audit_logs_aggregate: Auth_Audit_Logs_Aggregate;
	/** fetch data from the table: "auth_audit_logs" using primary key columns */
	auth_audit_logs_by_pk?: Maybe<Auth_Audit_Logs>;
	/** fetch data from the table in a streaming manner: "auth_audit_logs" */
	auth_audit_logs_stream: Array<Auth_Audit_Logs>;
	/** An array relationship */
	organization_domains: Array<Organization_Domains>;
	/** An aggregate relationship */
	organization_domains_aggregate: Organization_Domains_Aggregate;
	/** fetch data from the table: "organization_domains" using primary key columns */
	organization_domains_by_pk?: Maybe<Organization_Domains>;
	/** fetch data from the table in a streaming manner: "organization_domains" */
	organization_domains_stream: Array<Organization_Domains>;
	/** An array relationship */
	organization_invitations: Array<Organization_Invitations>;
	/** An aggregate relationship */
	organization_invitations_aggregate: Organization_Invitations_Aggregate;
	/** fetch data from the table: "organization_invitations" using primary key columns */
	organization_invitations_by_pk?: Maybe<Organization_Invitations>;
	/** fetch data from the table in a streaming manner: "organization_invitations" */
	organization_invitations_stream: Array<Organization_Invitations>;
	/** fetch data from the table: "organizations" */
	organizations: Array<Organizations>;
	/** fetch aggregated fields from the table: "organizations" */
	organizations_aggregate: Organizations_Aggregate;
	/** fetch data from the table: "organizations" using primary key columns */
	organizations_by_pk?: Maybe<Organizations>;
	/** fetch data from the table in a streaming manner: "organizations" */
	organizations_stream: Array<Organizations>;
	/** fetch data from the table: "schema_migrations" */
	schema_migrations: Array<Schema_Migrations>;
	/** fetch aggregated fields from the table: "schema_migrations" */
	schema_migrations_aggregate: Schema_Migrations_Aggregate;
	/** fetch data from the table: "schema_migrations" using primary key columns */
	schema_migrations_by_pk?: Maybe<Schema_Migrations>;
	/** fetch data from the table in a streaming manner: "schema_migrations" */
	schema_migrations_stream: Array<Schema_Migrations>;
	/** An array relationship */
	user_organizations: Array<User_Organizations>;
	/** An aggregate relationship */
	user_organizations_aggregate: User_Organizations_Aggregate;
	/** fetch data from the table: "user_organizations" using primary key columns */
	user_organizations_by_pk?: Maybe<User_Organizations>;
	/** fetch data from the table in a streaming manner: "user_organizations" */
	user_organizations_stream: Array<User_Organizations>;
	/** fetch data from the table: "users" */
	users: Array<Users>;
	/** fetch aggregated fields from the table: "users" */
	users_aggregate: Users_Aggregate;
	/** fetch data from the table: "users" using primary key columns */
	users_by_pk?: Maybe<Users>;
	/** fetch data from the table in a streaming manner: "users" */
	users_stream: Array<Users>;
	/** fetch data from the table: "webhook_logs" */
	webhook_logs: Array<Webhook_Logs>;
	/** fetch aggregated fields from the table: "webhook_logs" */
	webhook_logs_aggregate: Webhook_Logs_Aggregate;
	/** fetch data from the table: "webhook_logs" using primary key columns */
	webhook_logs_by_pk?: Maybe<Webhook_Logs>;
	/** fetch data from the table in a streaming manner: "webhook_logs" */
	webhook_logs_stream: Array<Webhook_Logs>;
};

export type Subscription_RootAuth_Audit_LogsArgs = {
	distinct_on?: InputMaybe<Array<Auth_Audit_Logs_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Auth_Audit_Logs_Order_By>>;
	where?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
};

export type Subscription_RootAuth_Audit_Logs_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Auth_Audit_Logs_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Auth_Audit_Logs_Order_By>>;
	where?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
};

export type Subscription_RootAuth_Audit_Logs_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

export type Subscription_RootAuth_Audit_Logs_StreamArgs = {
	batch_size: Scalars['Int']['input'];
	cursor: Array<InputMaybe<Auth_Audit_Logs_Stream_Cursor_Input>>;
	where?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
};

export type Subscription_RootOrganization_DomainsArgs = {
	distinct_on?: InputMaybe<Array<Organization_Domains_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Domains_Order_By>>;
	where?: InputMaybe<Organization_Domains_Bool_Exp>;
};

export type Subscription_RootOrganization_Domains_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Organization_Domains_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Domains_Order_By>>;
	where?: InputMaybe<Organization_Domains_Bool_Exp>;
};

export type Subscription_RootOrganization_Domains_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

export type Subscription_RootOrganization_Domains_StreamArgs = {
	batch_size: Scalars['Int']['input'];
	cursor: Array<InputMaybe<Organization_Domains_Stream_Cursor_Input>>;
	where?: InputMaybe<Organization_Domains_Bool_Exp>;
};

export type Subscription_RootOrganization_InvitationsArgs = {
	distinct_on?: InputMaybe<Array<Organization_Invitations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Invitations_Order_By>>;
	where?: InputMaybe<Organization_Invitations_Bool_Exp>;
};

export type Subscription_RootOrganization_Invitations_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Organization_Invitations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Invitations_Order_By>>;
	where?: InputMaybe<Organization_Invitations_Bool_Exp>;
};

export type Subscription_RootOrganization_Invitations_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

export type Subscription_RootOrganization_Invitations_StreamArgs = {
	batch_size: Scalars['Int']['input'];
	cursor: Array<InputMaybe<Organization_Invitations_Stream_Cursor_Input>>;
	where?: InputMaybe<Organization_Invitations_Bool_Exp>;
};

export type Subscription_RootOrganizationsArgs = {
	distinct_on?: InputMaybe<Array<Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organizations_Order_By>>;
	where?: InputMaybe<Organizations_Bool_Exp>;
};

export type Subscription_RootOrganizations_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organizations_Order_By>>;
	where?: InputMaybe<Organizations_Bool_Exp>;
};

export type Subscription_RootOrganizations_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

export type Subscription_RootOrganizations_StreamArgs = {
	batch_size: Scalars['Int']['input'];
	cursor: Array<InputMaybe<Organizations_Stream_Cursor_Input>>;
	where?: InputMaybe<Organizations_Bool_Exp>;
};

export type Subscription_RootSchema_MigrationsArgs = {
	distinct_on?: InputMaybe<Array<Schema_Migrations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Schema_Migrations_Order_By>>;
	where?: InputMaybe<Schema_Migrations_Bool_Exp>;
};

export type Subscription_RootSchema_Migrations_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Schema_Migrations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Schema_Migrations_Order_By>>;
	where?: InputMaybe<Schema_Migrations_Bool_Exp>;
};

export type Subscription_RootSchema_Migrations_By_PkArgs = {
	id: Scalars['Int']['input'];
};

export type Subscription_RootSchema_Migrations_StreamArgs = {
	batch_size: Scalars['Int']['input'];
	cursor: Array<InputMaybe<Schema_Migrations_Stream_Cursor_Input>>;
	where?: InputMaybe<Schema_Migrations_Bool_Exp>;
};

export type Subscription_RootUser_OrganizationsArgs = {
	distinct_on?: InputMaybe<Array<User_Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<User_Organizations_Order_By>>;
	where?: InputMaybe<User_Organizations_Bool_Exp>;
};

export type Subscription_RootUser_Organizations_AggregateArgs = {
	distinct_on?: InputMaybe<Array<User_Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<User_Organizations_Order_By>>;
	where?: InputMaybe<User_Organizations_Bool_Exp>;
};

export type Subscription_RootUser_Organizations_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

export type Subscription_RootUser_Organizations_StreamArgs = {
	batch_size: Scalars['Int']['input'];
	cursor: Array<InputMaybe<User_Organizations_Stream_Cursor_Input>>;
	where?: InputMaybe<User_Organizations_Bool_Exp>;
};

export type Subscription_RootUsersArgs = {
	distinct_on?: InputMaybe<Array<Users_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Users_Order_By>>;
	where?: InputMaybe<Users_Bool_Exp>;
};

export type Subscription_RootUsers_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Users_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Users_Order_By>>;
	where?: InputMaybe<Users_Bool_Exp>;
};

export type Subscription_RootUsers_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

export type Subscription_RootUsers_StreamArgs = {
	batch_size: Scalars['Int']['input'];
	cursor: Array<InputMaybe<Users_Stream_Cursor_Input>>;
	where?: InputMaybe<Users_Bool_Exp>;
};

export type Subscription_RootWebhook_LogsArgs = {
	distinct_on?: InputMaybe<Array<Webhook_Logs_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Webhook_Logs_Order_By>>;
	where?: InputMaybe<Webhook_Logs_Bool_Exp>;
};

export type Subscription_RootWebhook_Logs_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Webhook_Logs_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Webhook_Logs_Order_By>>;
	where?: InputMaybe<Webhook_Logs_Bool_Exp>;
};

export type Subscription_RootWebhook_Logs_By_PkArgs = {
	id: Scalars['uuid']['input'];
};

export type Subscription_RootWebhook_Logs_StreamArgs = {
	batch_size: Scalars['Int']['input'];
	cursor: Array<InputMaybe<Webhook_Logs_Stream_Cursor_Input>>;
	where?: InputMaybe<Webhook_Logs_Bool_Exp>;
};

/** Boolean expression to compare columns of type "timestamptz". All fields are combined with logical 'AND'. */
export type Timestamptz_Comparison_Exp = {
	_eq?: InputMaybe<Scalars['timestamptz']['input']>;
	_gt?: InputMaybe<Scalars['timestamptz']['input']>;
	_gte?: InputMaybe<Scalars['timestamptz']['input']>;
	_in?: InputMaybe<Array<Scalars['timestamptz']['input']>>;
	_is_null?: InputMaybe<Scalars['Boolean']['input']>;
	_lt?: InputMaybe<Scalars['timestamptz']['input']>;
	_lte?: InputMaybe<Scalars['timestamptz']['input']>;
	_neq?: InputMaybe<Scalars['timestamptz']['input']>;
	_nin?: InputMaybe<Array<Scalars['timestamptz']['input']>>;
};

/** columns and relationships of "user_organizations" */
export type User_Organizations = {
	__typename?: 'user_organizations';
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	id: Scalars['uuid']['output'];
	invited_at?: Maybe<Scalars['timestamptz']['output']>;
	invited_by?: Maybe<Scalars['uuid']['output']>;
	/** An object relationship */
	invited_by_user?: Maybe<Users>;
	joined_at?: Maybe<Scalars['timestamptz']['output']>;
	/** An object relationship */
	organization?: Maybe<Organizations>;
	organization_id?: Maybe<Scalars['uuid']['output']>;
	role: Scalars['String']['output'];
	status?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
	/** An object relationship */
	user?: Maybe<Users>;
	/** An object relationship */
	userByUserId?: Maybe<Users>;
	user_id?: Maybe<Scalars['uuid']['output']>;
};

/** aggregated selection of "user_organizations" */
export type User_Organizations_Aggregate = {
	__typename?: 'user_organizations_aggregate';
	aggregate?: Maybe<User_Organizations_Aggregate_Fields>;
	nodes: Array<User_Organizations>;
};

export type User_Organizations_Aggregate_Bool_Exp = {
	count?: InputMaybe<User_Organizations_Aggregate_Bool_Exp_Count>;
};

export type User_Organizations_Aggregate_Bool_Exp_Count = {
	arguments?: InputMaybe<Array<User_Organizations_Select_Column>>;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
	filter?: InputMaybe<User_Organizations_Bool_Exp>;
	predicate: Int_Comparison_Exp;
};

/** aggregate fields of "user_organizations" */
export type User_Organizations_Aggregate_Fields = {
	__typename?: 'user_organizations_aggregate_fields';
	count: Scalars['Int']['output'];
	max?: Maybe<User_Organizations_Max_Fields>;
	min?: Maybe<User_Organizations_Min_Fields>;
};

/** aggregate fields of "user_organizations" */
export type User_Organizations_Aggregate_FieldsCountArgs = {
	columns?: InputMaybe<Array<User_Organizations_Select_Column>>;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
};

/** order by aggregate values of table "user_organizations" */
export type User_Organizations_Aggregate_Order_By = {
	count?: InputMaybe<Order_By>;
	max?: InputMaybe<User_Organizations_Max_Order_By>;
	min?: InputMaybe<User_Organizations_Min_Order_By>;
};

/** input type for inserting array relation for remote table "user_organizations" */
export type User_Organizations_Arr_Rel_Insert_Input = {
	data: Array<User_Organizations_Insert_Input>;
	/** upsert condition */
	on_conflict?: InputMaybe<User_Organizations_On_Conflict>;
};

/** Boolean expression to filter rows from the table "user_organizations". All fields are combined with a logical 'AND'. */
export type User_Organizations_Bool_Exp = {
	_and?: InputMaybe<Array<User_Organizations_Bool_Exp>>;
	_not?: InputMaybe<User_Organizations_Bool_Exp>;
	_or?: InputMaybe<Array<User_Organizations_Bool_Exp>>;
	created_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	id?: InputMaybe<Uuid_Comparison_Exp>;
	invited_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	invited_by?: InputMaybe<Uuid_Comparison_Exp>;
	invited_by_user?: InputMaybe<Users_Bool_Exp>;
	joined_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	organization?: InputMaybe<Organizations_Bool_Exp>;
	organization_id?: InputMaybe<Uuid_Comparison_Exp>;
	role?: InputMaybe<String_Comparison_Exp>;
	status?: InputMaybe<String_Comparison_Exp>;
	updated_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	user?: InputMaybe<Users_Bool_Exp>;
	userByUserId?: InputMaybe<Users_Bool_Exp>;
	user_id?: InputMaybe<Uuid_Comparison_Exp>;
};

/** unique or primary key constraints on table "user_organizations" */
export enum User_Organizations_Constraint {
	/** unique or primary key constraint on columns "id" */
	UserOrganizationsPkey = 'user_organizations_pkey',
	/** unique or primary key constraint on columns "user_id", "organization_id" */
	UserOrganizationsUserIdOrganizationIdKey = 'user_organizations_user_id_organization_id_key'
}

/** input type for inserting data into table "user_organizations" */
export type User_Organizations_Insert_Input = {
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	invited_at?: InputMaybe<Scalars['timestamptz']['input']>;
	invited_by?: InputMaybe<Scalars['uuid']['input']>;
	invited_by_user?: InputMaybe<Users_Obj_Rel_Insert_Input>;
	joined_at?: InputMaybe<Scalars['timestamptz']['input']>;
	organization?: InputMaybe<Organizations_Obj_Rel_Insert_Input>;
	organization_id?: InputMaybe<Scalars['uuid']['input']>;
	role?: InputMaybe<Scalars['String']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
	user?: InputMaybe<Users_Obj_Rel_Insert_Input>;
	userByUserId?: InputMaybe<Users_Obj_Rel_Insert_Input>;
	user_id?: InputMaybe<Scalars['uuid']['input']>;
};

/** aggregate max on columns */
export type User_Organizations_Max_Fields = {
	__typename?: 'user_organizations_max_fields';
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	invited_at?: Maybe<Scalars['timestamptz']['output']>;
	invited_by?: Maybe<Scalars['uuid']['output']>;
	joined_at?: Maybe<Scalars['timestamptz']['output']>;
	organization_id?: Maybe<Scalars['uuid']['output']>;
	role?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
	user_id?: Maybe<Scalars['uuid']['output']>;
};

/** order by max() on columns of table "user_organizations" */
export type User_Organizations_Max_Order_By = {
	created_at?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	invited_at?: InputMaybe<Order_By>;
	invited_by?: InputMaybe<Order_By>;
	joined_at?: InputMaybe<Order_By>;
	organization_id?: InputMaybe<Order_By>;
	role?: InputMaybe<Order_By>;
	status?: InputMaybe<Order_By>;
	updated_at?: InputMaybe<Order_By>;
	user_id?: InputMaybe<Order_By>;
};

/** aggregate min on columns */
export type User_Organizations_Min_Fields = {
	__typename?: 'user_organizations_min_fields';
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	invited_at?: Maybe<Scalars['timestamptz']['output']>;
	invited_by?: Maybe<Scalars['uuid']['output']>;
	joined_at?: Maybe<Scalars['timestamptz']['output']>;
	organization_id?: Maybe<Scalars['uuid']['output']>;
	role?: Maybe<Scalars['String']['output']>;
	status?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
	user_id?: Maybe<Scalars['uuid']['output']>;
};

/** order by min() on columns of table "user_organizations" */
export type User_Organizations_Min_Order_By = {
	created_at?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	invited_at?: InputMaybe<Order_By>;
	invited_by?: InputMaybe<Order_By>;
	joined_at?: InputMaybe<Order_By>;
	organization_id?: InputMaybe<Order_By>;
	role?: InputMaybe<Order_By>;
	status?: InputMaybe<Order_By>;
	updated_at?: InputMaybe<Order_By>;
	user_id?: InputMaybe<Order_By>;
};

/** response of any mutation on the table "user_organizations" */
export type User_Organizations_Mutation_Response = {
	__typename?: 'user_organizations_mutation_response';
	/** number of rows affected by the mutation */
	affected_rows: Scalars['Int']['output'];
	/** data from the rows affected by the mutation */
	returning: Array<User_Organizations>;
};

/** on_conflict condition type for table "user_organizations" */
export type User_Organizations_On_Conflict = {
	constraint: User_Organizations_Constraint;
	update_columns?: Array<User_Organizations_Update_Column>;
	where?: InputMaybe<User_Organizations_Bool_Exp>;
};

/** Ordering options when selecting data from "user_organizations". */
export type User_Organizations_Order_By = {
	created_at?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	invited_at?: InputMaybe<Order_By>;
	invited_by?: InputMaybe<Order_By>;
	invited_by_user?: InputMaybe<Users_Order_By>;
	joined_at?: InputMaybe<Order_By>;
	organization?: InputMaybe<Organizations_Order_By>;
	organization_id?: InputMaybe<Order_By>;
	role?: InputMaybe<Order_By>;
	status?: InputMaybe<Order_By>;
	updated_at?: InputMaybe<Order_By>;
	user?: InputMaybe<Users_Order_By>;
	userByUserId?: InputMaybe<Users_Order_By>;
	user_id?: InputMaybe<Order_By>;
};

/** primary key columns input for table: user_organizations */
export type User_Organizations_Pk_Columns_Input = {
	id: Scalars['uuid']['input'];
};

/** select columns of table "user_organizations" */
export enum User_Organizations_Select_Column {
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	Id = 'id',
	/** column name */
	InvitedAt = 'invited_at',
	/** column name */
	InvitedBy = 'invited_by',
	/** column name */
	JoinedAt = 'joined_at',
	/** column name */
	OrganizationId = 'organization_id',
	/** column name */
	Role = 'role',
	/** column name */
	Status = 'status',
	/** column name */
	UpdatedAt = 'updated_at',
	/** column name */
	UserId = 'user_id'
}

/** input type for updating data in table "user_organizations" */
export type User_Organizations_Set_Input = {
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	invited_at?: InputMaybe<Scalars['timestamptz']['input']>;
	invited_by?: InputMaybe<Scalars['uuid']['input']>;
	joined_at?: InputMaybe<Scalars['timestamptz']['input']>;
	organization_id?: InputMaybe<Scalars['uuid']['input']>;
	role?: InputMaybe<Scalars['String']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
	user_id?: InputMaybe<Scalars['uuid']['input']>;
};

/** Streaming cursor of the table "user_organizations" */
export type User_Organizations_Stream_Cursor_Input = {
	/** Stream column input with initial value */
	initial_value: User_Organizations_Stream_Cursor_Value_Input;
	/** cursor ordering */
	ordering?: InputMaybe<Cursor_Ordering>;
};

/** Initial value of the column from where the streaming should start */
export type User_Organizations_Stream_Cursor_Value_Input = {
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	invited_at?: InputMaybe<Scalars['timestamptz']['input']>;
	invited_by?: InputMaybe<Scalars['uuid']['input']>;
	joined_at?: InputMaybe<Scalars['timestamptz']['input']>;
	organization_id?: InputMaybe<Scalars['uuid']['input']>;
	role?: InputMaybe<Scalars['String']['input']>;
	status?: InputMaybe<Scalars['String']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
	user_id?: InputMaybe<Scalars['uuid']['input']>;
};

/** update columns of table "user_organizations" */
export enum User_Organizations_Update_Column {
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	Id = 'id',
	/** column name */
	InvitedAt = 'invited_at',
	/** column name */
	InvitedBy = 'invited_by',
	/** column name */
	JoinedAt = 'joined_at',
	/** column name */
	OrganizationId = 'organization_id',
	/** column name */
	Role = 'role',
	/** column name */
	Status = 'status',
	/** column name */
	UpdatedAt = 'updated_at',
	/** column name */
	UserId = 'user_id'
}

export type User_Organizations_Updates = {
	/** sets the columns of the filtered rows to the given values */
	_set?: InputMaybe<User_Organizations_Set_Input>;
	/** filter the rows which have to be updated */
	where: User_Organizations_Bool_Exp;
};

/** columns and relationships of "users" */
export type Users = {
	__typename?: 'users';
	/** An array relationship */
	auth_audit_logs: Array<Auth_Audit_Logs>;
	/** An aggregate relationship */
	auth_audit_logs_aggregate: Auth_Audit_Logs_Aggregate;
	avatar_url?: Maybe<Scalars['String']['output']>;
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	email: Scalars['String']['output'];
	email_verified?: Maybe<Scalars['Boolean']['output']>;
	first_name?: Maybe<Scalars['String']['output']>;
	id: Scalars['uuid']['output'];
	is_active?: Maybe<Scalars['Boolean']['output']>;
	last_login_at?: Maybe<Scalars['timestamptz']['output']>;
	last_name?: Maybe<Scalars['String']['output']>;
	logto_user_id: Scalars['String']['output'];
	/** An array relationship */
	organization_invitations: Array<Organization_Invitations>;
	/** An aggregate relationship */
	organization_invitations_aggregate: Organization_Invitations_Aggregate;
	phone?: Maybe<Scalars['String']['output']>;
	phone_verified?: Maybe<Scalars['Boolean']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
	/** An array relationship */
	userOrganizationsByUserId: Array<User_Organizations>;
	/** An aggregate relationship */
	userOrganizationsByUserId_aggregate: User_Organizations_Aggregate;
	/** An array relationship */
	user_organizations: Array<User_Organizations>;
	/** An aggregate relationship */
	user_organizations_aggregate: User_Organizations_Aggregate;
	username?: Maybe<Scalars['String']['output']>;
};

/** columns and relationships of "users" */
export type UsersAuth_Audit_LogsArgs = {
	distinct_on?: InputMaybe<Array<Auth_Audit_Logs_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Auth_Audit_Logs_Order_By>>;
	where?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
};

/** columns and relationships of "users" */
export type UsersAuth_Audit_Logs_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Auth_Audit_Logs_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Auth_Audit_Logs_Order_By>>;
	where?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
};

/** columns and relationships of "users" */
export type UsersOrganization_InvitationsArgs = {
	distinct_on?: InputMaybe<Array<Organization_Invitations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Invitations_Order_By>>;
	where?: InputMaybe<Organization_Invitations_Bool_Exp>;
};

/** columns and relationships of "users" */
export type UsersOrganization_Invitations_AggregateArgs = {
	distinct_on?: InputMaybe<Array<Organization_Invitations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<Organization_Invitations_Order_By>>;
	where?: InputMaybe<Organization_Invitations_Bool_Exp>;
};

/** columns and relationships of "users" */
export type UsersUserOrganizationsByUserIdArgs = {
	distinct_on?: InputMaybe<Array<User_Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<User_Organizations_Order_By>>;
	where?: InputMaybe<User_Organizations_Bool_Exp>;
};

/** columns and relationships of "users" */
export type UsersUserOrganizationsByUserId_AggregateArgs = {
	distinct_on?: InputMaybe<Array<User_Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<User_Organizations_Order_By>>;
	where?: InputMaybe<User_Organizations_Bool_Exp>;
};

/** columns and relationships of "users" */
export type UsersUser_OrganizationsArgs = {
	distinct_on?: InputMaybe<Array<User_Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<User_Organizations_Order_By>>;
	where?: InputMaybe<User_Organizations_Bool_Exp>;
};

/** columns and relationships of "users" */
export type UsersUser_Organizations_AggregateArgs = {
	distinct_on?: InputMaybe<Array<User_Organizations_Select_Column>>;
	limit?: InputMaybe<Scalars['Int']['input']>;
	offset?: InputMaybe<Scalars['Int']['input']>;
	order_by?: InputMaybe<Array<User_Organizations_Order_By>>;
	where?: InputMaybe<User_Organizations_Bool_Exp>;
};

/** aggregated selection of "users" */
export type Users_Aggregate = {
	__typename?: 'users_aggregate';
	aggregate?: Maybe<Users_Aggregate_Fields>;
	nodes: Array<Users>;
};

/** aggregate fields of "users" */
export type Users_Aggregate_Fields = {
	__typename?: 'users_aggregate_fields';
	count: Scalars['Int']['output'];
	max?: Maybe<Users_Max_Fields>;
	min?: Maybe<Users_Min_Fields>;
};

/** aggregate fields of "users" */
export type Users_Aggregate_FieldsCountArgs = {
	columns?: InputMaybe<Array<Users_Select_Column>>;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
};

/** Boolean expression to filter rows from the table "users". All fields are combined with a logical 'AND'. */
export type Users_Bool_Exp = {
	_and?: InputMaybe<Array<Users_Bool_Exp>>;
	_not?: InputMaybe<Users_Bool_Exp>;
	_or?: InputMaybe<Array<Users_Bool_Exp>>;
	auth_audit_logs?: InputMaybe<Auth_Audit_Logs_Bool_Exp>;
	auth_audit_logs_aggregate?: InputMaybe<Auth_Audit_Logs_Aggregate_Bool_Exp>;
	avatar_url?: InputMaybe<String_Comparison_Exp>;
	created_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	email?: InputMaybe<String_Comparison_Exp>;
	email_verified?: InputMaybe<Boolean_Comparison_Exp>;
	first_name?: InputMaybe<String_Comparison_Exp>;
	id?: InputMaybe<Uuid_Comparison_Exp>;
	is_active?: InputMaybe<Boolean_Comparison_Exp>;
	last_login_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	last_name?: InputMaybe<String_Comparison_Exp>;
	logto_user_id?: InputMaybe<String_Comparison_Exp>;
	organization_invitations?: InputMaybe<Organization_Invitations_Bool_Exp>;
	organization_invitations_aggregate?: InputMaybe<Organization_Invitations_Aggregate_Bool_Exp>;
	phone?: InputMaybe<String_Comparison_Exp>;
	phone_verified?: InputMaybe<Boolean_Comparison_Exp>;
	updated_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	userOrganizationsByUserId?: InputMaybe<User_Organizations_Bool_Exp>;
	userOrganizationsByUserId_aggregate?: InputMaybe<User_Organizations_Aggregate_Bool_Exp>;
	user_organizations?: InputMaybe<User_Organizations_Bool_Exp>;
	user_organizations_aggregate?: InputMaybe<User_Organizations_Aggregate_Bool_Exp>;
	username?: InputMaybe<String_Comparison_Exp>;
};

/** unique or primary key constraints on table "users" */
export enum Users_Constraint {
	/** unique or primary key constraint on columns "email" */
	UsersEmailKey = 'users_email_key',
	/** unique or primary key constraint on columns "logto_user_id" */
	UsersLogtoUserIdKey = 'users_logto_user_id_key',
	/** unique or primary key constraint on columns "id" */
	UsersPkey = 'users_pkey'
}

/** input type for inserting data into table "users" */
export type Users_Insert_Input = {
	auth_audit_logs?: InputMaybe<Auth_Audit_Logs_Arr_Rel_Insert_Input>;
	avatar_url?: InputMaybe<Scalars['String']['input']>;
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	email?: InputMaybe<Scalars['String']['input']>;
	email_verified?: InputMaybe<Scalars['Boolean']['input']>;
	first_name?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	is_active?: InputMaybe<Scalars['Boolean']['input']>;
	last_login_at?: InputMaybe<Scalars['timestamptz']['input']>;
	last_name?: InputMaybe<Scalars['String']['input']>;
	logto_user_id?: InputMaybe<Scalars['String']['input']>;
	organization_invitations?: InputMaybe<Organization_Invitations_Arr_Rel_Insert_Input>;
	phone?: InputMaybe<Scalars['String']['input']>;
	phone_verified?: InputMaybe<Scalars['Boolean']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
	userOrganizationsByUserId?: InputMaybe<User_Organizations_Arr_Rel_Insert_Input>;
	user_organizations?: InputMaybe<User_Organizations_Arr_Rel_Insert_Input>;
	username?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate max on columns */
export type Users_Max_Fields = {
	__typename?: 'users_max_fields';
	avatar_url?: Maybe<Scalars['String']['output']>;
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	email?: Maybe<Scalars['String']['output']>;
	first_name?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	last_login_at?: Maybe<Scalars['timestamptz']['output']>;
	last_name?: Maybe<Scalars['String']['output']>;
	logto_user_id?: Maybe<Scalars['String']['output']>;
	phone?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
	username?: Maybe<Scalars['String']['output']>;
};

/** aggregate min on columns */
export type Users_Min_Fields = {
	__typename?: 'users_min_fields';
	avatar_url?: Maybe<Scalars['String']['output']>;
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	email?: Maybe<Scalars['String']['output']>;
	first_name?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	last_login_at?: Maybe<Scalars['timestamptz']['output']>;
	last_name?: Maybe<Scalars['String']['output']>;
	logto_user_id?: Maybe<Scalars['String']['output']>;
	phone?: Maybe<Scalars['String']['output']>;
	updated_at?: Maybe<Scalars['timestamptz']['output']>;
	username?: Maybe<Scalars['String']['output']>;
};

/** response of any mutation on the table "users" */
export type Users_Mutation_Response = {
	__typename?: 'users_mutation_response';
	/** number of rows affected by the mutation */
	affected_rows: Scalars['Int']['output'];
	/** data from the rows affected by the mutation */
	returning: Array<Users>;
};

/** input type for inserting object relation for remote table "users" */
export type Users_Obj_Rel_Insert_Input = {
	data: Users_Insert_Input;
	/** upsert condition */
	on_conflict?: InputMaybe<Users_On_Conflict>;
};

/** on_conflict condition type for table "users" */
export type Users_On_Conflict = {
	constraint: Users_Constraint;
	update_columns?: Array<Users_Update_Column>;
	where?: InputMaybe<Users_Bool_Exp>;
};

/** Ordering options when selecting data from "users". */
export type Users_Order_By = {
	auth_audit_logs_aggregate?: InputMaybe<Auth_Audit_Logs_Aggregate_Order_By>;
	avatar_url?: InputMaybe<Order_By>;
	created_at?: InputMaybe<Order_By>;
	email?: InputMaybe<Order_By>;
	email_verified?: InputMaybe<Order_By>;
	first_name?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	is_active?: InputMaybe<Order_By>;
	last_login_at?: InputMaybe<Order_By>;
	last_name?: InputMaybe<Order_By>;
	logto_user_id?: InputMaybe<Order_By>;
	organization_invitations_aggregate?: InputMaybe<Organization_Invitations_Aggregate_Order_By>;
	phone?: InputMaybe<Order_By>;
	phone_verified?: InputMaybe<Order_By>;
	updated_at?: InputMaybe<Order_By>;
	userOrganizationsByUserId_aggregate?: InputMaybe<User_Organizations_Aggregate_Order_By>;
	user_organizations_aggregate?: InputMaybe<User_Organizations_Aggregate_Order_By>;
	username?: InputMaybe<Order_By>;
};

/** primary key columns input for table: users */
export type Users_Pk_Columns_Input = {
	id: Scalars['uuid']['input'];
};

/** select columns of table "users" */
export enum Users_Select_Column {
	/** column name */
	AvatarUrl = 'avatar_url',
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	Email = 'email',
	/** column name */
	EmailVerified = 'email_verified',
	/** column name */
	FirstName = 'first_name',
	/** column name */
	Id = 'id',
	/** column name */
	IsActive = 'is_active',
	/** column name */
	LastLoginAt = 'last_login_at',
	/** column name */
	LastName = 'last_name',
	/** column name */
	LogtoUserId = 'logto_user_id',
	/** column name */
	Phone = 'phone',
	/** column name */
	PhoneVerified = 'phone_verified',
	/** column name */
	UpdatedAt = 'updated_at',
	/** column name */
	Username = 'username'
}

/** input type for updating data in table "users" */
export type Users_Set_Input = {
	avatar_url?: InputMaybe<Scalars['String']['input']>;
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	email?: InputMaybe<Scalars['String']['input']>;
	email_verified?: InputMaybe<Scalars['Boolean']['input']>;
	first_name?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	is_active?: InputMaybe<Scalars['Boolean']['input']>;
	last_login_at?: InputMaybe<Scalars['timestamptz']['input']>;
	last_name?: InputMaybe<Scalars['String']['input']>;
	logto_user_id?: InputMaybe<Scalars['String']['input']>;
	phone?: InputMaybe<Scalars['String']['input']>;
	phone_verified?: InputMaybe<Scalars['Boolean']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
	username?: InputMaybe<Scalars['String']['input']>;
};

/** Streaming cursor of the table "users" */
export type Users_Stream_Cursor_Input = {
	/** Stream column input with initial value */
	initial_value: Users_Stream_Cursor_Value_Input;
	/** cursor ordering */
	ordering?: InputMaybe<Cursor_Ordering>;
};

/** Initial value of the column from where the streaming should start */
export type Users_Stream_Cursor_Value_Input = {
	avatar_url?: InputMaybe<Scalars['String']['input']>;
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	email?: InputMaybe<Scalars['String']['input']>;
	email_verified?: InputMaybe<Scalars['Boolean']['input']>;
	first_name?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	is_active?: InputMaybe<Scalars['Boolean']['input']>;
	last_login_at?: InputMaybe<Scalars['timestamptz']['input']>;
	last_name?: InputMaybe<Scalars['String']['input']>;
	logto_user_id?: InputMaybe<Scalars['String']['input']>;
	phone?: InputMaybe<Scalars['String']['input']>;
	phone_verified?: InputMaybe<Scalars['Boolean']['input']>;
	updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
	username?: InputMaybe<Scalars['String']['input']>;
};

/** update columns of table "users" */
export enum Users_Update_Column {
	/** column name */
	AvatarUrl = 'avatar_url',
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	Email = 'email',
	/** column name */
	EmailVerified = 'email_verified',
	/** column name */
	FirstName = 'first_name',
	/** column name */
	Id = 'id',
	/** column name */
	IsActive = 'is_active',
	/** column name */
	LastLoginAt = 'last_login_at',
	/** column name */
	LastName = 'last_name',
	/** column name */
	LogtoUserId = 'logto_user_id',
	/** column name */
	Phone = 'phone',
	/** column name */
	PhoneVerified = 'phone_verified',
	/** column name */
	UpdatedAt = 'updated_at',
	/** column name */
	Username = 'username'
}

export type Users_Updates = {
	/** sets the columns of the filtered rows to the given values */
	_set?: InputMaybe<Users_Set_Input>;
	/** filter the rows which have to be updated */
	where: Users_Bool_Exp;
};

/** Boolean expression to compare columns of type "uuid". All fields are combined with logical 'AND'. */
export type Uuid_Comparison_Exp = {
	_eq?: InputMaybe<Scalars['uuid']['input']>;
	_gt?: InputMaybe<Scalars['uuid']['input']>;
	_gte?: InputMaybe<Scalars['uuid']['input']>;
	_in?: InputMaybe<Array<Scalars['uuid']['input']>>;
	_is_null?: InputMaybe<Scalars['Boolean']['input']>;
	_lt?: InputMaybe<Scalars['uuid']['input']>;
	_lte?: InputMaybe<Scalars['uuid']['input']>;
	_neq?: InputMaybe<Scalars['uuid']['input']>;
	_nin?: InputMaybe<Array<Scalars['uuid']['input']>>;
};

/** columns and relationships of "webhook_logs" */
export type Webhook_Logs = {
	__typename?: 'webhook_logs';
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	error_message?: Maybe<Scalars['String']['output']>;
	id: Scalars['uuid']['output'];
	logto_event_id?: Maybe<Scalars['String']['output']>;
	payload: Scalars['jsonb']['output'];
	processed_at?: Maybe<Scalars['timestamptz']['output']>;
	processing_status?: Maybe<Scalars['String']['output']>;
	retry_count?: Maybe<Scalars['Int']['output']>;
	webhook_type: Scalars['String']['output'];
};

/** columns and relationships of "webhook_logs" */
export type Webhook_LogsPayloadArgs = {
	path?: InputMaybe<Scalars['String']['input']>;
};

/** aggregated selection of "webhook_logs" */
export type Webhook_Logs_Aggregate = {
	__typename?: 'webhook_logs_aggregate';
	aggregate?: Maybe<Webhook_Logs_Aggregate_Fields>;
	nodes: Array<Webhook_Logs>;
};

/** aggregate fields of "webhook_logs" */
export type Webhook_Logs_Aggregate_Fields = {
	__typename?: 'webhook_logs_aggregate_fields';
	avg?: Maybe<Webhook_Logs_Avg_Fields>;
	count: Scalars['Int']['output'];
	max?: Maybe<Webhook_Logs_Max_Fields>;
	min?: Maybe<Webhook_Logs_Min_Fields>;
	stddev?: Maybe<Webhook_Logs_Stddev_Fields>;
	stddev_pop?: Maybe<Webhook_Logs_Stddev_Pop_Fields>;
	stddev_samp?: Maybe<Webhook_Logs_Stddev_Samp_Fields>;
	sum?: Maybe<Webhook_Logs_Sum_Fields>;
	var_pop?: Maybe<Webhook_Logs_Var_Pop_Fields>;
	var_samp?: Maybe<Webhook_Logs_Var_Samp_Fields>;
	variance?: Maybe<Webhook_Logs_Variance_Fields>;
};

/** aggregate fields of "webhook_logs" */
export type Webhook_Logs_Aggregate_FieldsCountArgs = {
	columns?: InputMaybe<Array<Webhook_Logs_Select_Column>>;
	distinct?: InputMaybe<Scalars['Boolean']['input']>;
};

/** append existing jsonb value of filtered columns with new jsonb value */
export type Webhook_Logs_Append_Input = {
	payload?: InputMaybe<Scalars['jsonb']['input']>;
};

/** aggregate avg on columns */
export type Webhook_Logs_Avg_Fields = {
	__typename?: 'webhook_logs_avg_fields';
	retry_count?: Maybe<Scalars['Float']['output']>;
};

/** Boolean expression to filter rows from the table "webhook_logs". All fields are combined with a logical 'AND'. */
export type Webhook_Logs_Bool_Exp = {
	_and?: InputMaybe<Array<Webhook_Logs_Bool_Exp>>;
	_not?: InputMaybe<Webhook_Logs_Bool_Exp>;
	_or?: InputMaybe<Array<Webhook_Logs_Bool_Exp>>;
	created_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	error_message?: InputMaybe<String_Comparison_Exp>;
	id?: InputMaybe<Uuid_Comparison_Exp>;
	logto_event_id?: InputMaybe<String_Comparison_Exp>;
	payload?: InputMaybe<Jsonb_Comparison_Exp>;
	processed_at?: InputMaybe<Timestamptz_Comparison_Exp>;
	processing_status?: InputMaybe<String_Comparison_Exp>;
	retry_count?: InputMaybe<Int_Comparison_Exp>;
	webhook_type?: InputMaybe<String_Comparison_Exp>;
};

/** unique or primary key constraints on table "webhook_logs" */
export enum Webhook_Logs_Constraint {
	/** unique or primary key constraint on columns "id" */
	WebhookLogsPkey = 'webhook_logs_pkey'
}

/** delete the field or element with specified path (for JSON arrays, negative integers count from the end) */
export type Webhook_Logs_Delete_At_Path_Input = {
	payload?: InputMaybe<Array<Scalars['String']['input']>>;
};

/** delete the array element with specified index (negative integers count from the end). throws an error if top level container is not an array */
export type Webhook_Logs_Delete_Elem_Input = {
	payload?: InputMaybe<Scalars['Int']['input']>;
};

/** delete key/value pair or string element. key/value pairs are matched based on their key value */
export type Webhook_Logs_Delete_Key_Input = {
	payload?: InputMaybe<Scalars['String']['input']>;
};

/** input type for incrementing numeric columns in table "webhook_logs" */
export type Webhook_Logs_Inc_Input = {
	retry_count?: InputMaybe<Scalars['Int']['input']>;
};

/** input type for inserting data into table "webhook_logs" */
export type Webhook_Logs_Insert_Input = {
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	error_message?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	logto_event_id?: InputMaybe<Scalars['String']['input']>;
	payload?: InputMaybe<Scalars['jsonb']['input']>;
	processed_at?: InputMaybe<Scalars['timestamptz']['input']>;
	processing_status?: InputMaybe<Scalars['String']['input']>;
	retry_count?: InputMaybe<Scalars['Int']['input']>;
	webhook_type?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate max on columns */
export type Webhook_Logs_Max_Fields = {
	__typename?: 'webhook_logs_max_fields';
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	error_message?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	logto_event_id?: Maybe<Scalars['String']['output']>;
	processed_at?: Maybe<Scalars['timestamptz']['output']>;
	processing_status?: Maybe<Scalars['String']['output']>;
	retry_count?: Maybe<Scalars['Int']['output']>;
	webhook_type?: Maybe<Scalars['String']['output']>;
};

/** aggregate min on columns */
export type Webhook_Logs_Min_Fields = {
	__typename?: 'webhook_logs_min_fields';
	created_at?: Maybe<Scalars['timestamptz']['output']>;
	error_message?: Maybe<Scalars['String']['output']>;
	id?: Maybe<Scalars['uuid']['output']>;
	logto_event_id?: Maybe<Scalars['String']['output']>;
	processed_at?: Maybe<Scalars['timestamptz']['output']>;
	processing_status?: Maybe<Scalars['String']['output']>;
	retry_count?: Maybe<Scalars['Int']['output']>;
	webhook_type?: Maybe<Scalars['String']['output']>;
};

/** response of any mutation on the table "webhook_logs" */
export type Webhook_Logs_Mutation_Response = {
	__typename?: 'webhook_logs_mutation_response';
	/** number of rows affected by the mutation */
	affected_rows: Scalars['Int']['output'];
	/** data from the rows affected by the mutation */
	returning: Array<Webhook_Logs>;
};

/** on_conflict condition type for table "webhook_logs" */
export type Webhook_Logs_On_Conflict = {
	constraint: Webhook_Logs_Constraint;
	update_columns?: Array<Webhook_Logs_Update_Column>;
	where?: InputMaybe<Webhook_Logs_Bool_Exp>;
};

/** Ordering options when selecting data from "webhook_logs". */
export type Webhook_Logs_Order_By = {
	created_at?: InputMaybe<Order_By>;
	error_message?: InputMaybe<Order_By>;
	id?: InputMaybe<Order_By>;
	logto_event_id?: InputMaybe<Order_By>;
	payload?: InputMaybe<Order_By>;
	processed_at?: InputMaybe<Order_By>;
	processing_status?: InputMaybe<Order_By>;
	retry_count?: InputMaybe<Order_By>;
	webhook_type?: InputMaybe<Order_By>;
};

/** primary key columns input for table: webhook_logs */
export type Webhook_Logs_Pk_Columns_Input = {
	id: Scalars['uuid']['input'];
};

/** prepend existing jsonb value of filtered columns with new jsonb value */
export type Webhook_Logs_Prepend_Input = {
	payload?: InputMaybe<Scalars['jsonb']['input']>;
};

/** select columns of table "webhook_logs" */
export enum Webhook_Logs_Select_Column {
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	ErrorMessage = 'error_message',
	/** column name */
	Id = 'id',
	/** column name */
	LogtoEventId = 'logto_event_id',
	/** column name */
	Payload = 'payload',
	/** column name */
	ProcessedAt = 'processed_at',
	/** column name */
	ProcessingStatus = 'processing_status',
	/** column name */
	RetryCount = 'retry_count',
	/** column name */
	WebhookType = 'webhook_type'
}

/** input type for updating data in table "webhook_logs" */
export type Webhook_Logs_Set_Input = {
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	error_message?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	logto_event_id?: InputMaybe<Scalars['String']['input']>;
	payload?: InputMaybe<Scalars['jsonb']['input']>;
	processed_at?: InputMaybe<Scalars['timestamptz']['input']>;
	processing_status?: InputMaybe<Scalars['String']['input']>;
	retry_count?: InputMaybe<Scalars['Int']['input']>;
	webhook_type?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate stddev on columns */
export type Webhook_Logs_Stddev_Fields = {
	__typename?: 'webhook_logs_stddev_fields';
	retry_count?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_pop on columns */
export type Webhook_Logs_Stddev_Pop_Fields = {
	__typename?: 'webhook_logs_stddev_pop_fields';
	retry_count?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_samp on columns */
export type Webhook_Logs_Stddev_Samp_Fields = {
	__typename?: 'webhook_logs_stddev_samp_fields';
	retry_count?: Maybe<Scalars['Float']['output']>;
};

/** Streaming cursor of the table "webhook_logs" */
export type Webhook_Logs_Stream_Cursor_Input = {
	/** Stream column input with initial value */
	initial_value: Webhook_Logs_Stream_Cursor_Value_Input;
	/** cursor ordering */
	ordering?: InputMaybe<Cursor_Ordering>;
};

/** Initial value of the column from where the streaming should start */
export type Webhook_Logs_Stream_Cursor_Value_Input = {
	created_at?: InputMaybe<Scalars['timestamptz']['input']>;
	error_message?: InputMaybe<Scalars['String']['input']>;
	id?: InputMaybe<Scalars['uuid']['input']>;
	logto_event_id?: InputMaybe<Scalars['String']['input']>;
	payload?: InputMaybe<Scalars['jsonb']['input']>;
	processed_at?: InputMaybe<Scalars['timestamptz']['input']>;
	processing_status?: InputMaybe<Scalars['String']['input']>;
	retry_count?: InputMaybe<Scalars['Int']['input']>;
	webhook_type?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate sum on columns */
export type Webhook_Logs_Sum_Fields = {
	__typename?: 'webhook_logs_sum_fields';
	retry_count?: Maybe<Scalars['Int']['output']>;
};

/** update columns of table "webhook_logs" */
export enum Webhook_Logs_Update_Column {
	/** column name */
	CreatedAt = 'created_at',
	/** column name */
	ErrorMessage = 'error_message',
	/** column name */
	Id = 'id',
	/** column name */
	LogtoEventId = 'logto_event_id',
	/** column name */
	Payload = 'payload',
	/** column name */
	ProcessedAt = 'processed_at',
	/** column name */
	ProcessingStatus = 'processing_status',
	/** column name */
	RetryCount = 'retry_count',
	/** column name */
	WebhookType = 'webhook_type'
}

export type Webhook_Logs_Updates = {
	/** append existing jsonb value of filtered columns with new jsonb value */
	_append?: InputMaybe<Webhook_Logs_Append_Input>;
	/** delete the field or element with specified path (for JSON arrays, negative integers count from the end) */
	_delete_at_path?: InputMaybe<Webhook_Logs_Delete_At_Path_Input>;
	/** delete the array element with specified index (negative integers count from the end). throws an error if top level container is not an array */
	_delete_elem?: InputMaybe<Webhook_Logs_Delete_Elem_Input>;
	/** delete key/value pair or string element. key/value pairs are matched based on their key value */
	_delete_key?: InputMaybe<Webhook_Logs_Delete_Key_Input>;
	/** increments the numeric columns with given value of the filtered values */
	_inc?: InputMaybe<Webhook_Logs_Inc_Input>;
	/** prepend existing jsonb value of filtered columns with new jsonb value */
	_prepend?: InputMaybe<Webhook_Logs_Prepend_Input>;
	/** sets the columns of the filtered rows to the given values */
	_set?: InputMaybe<Webhook_Logs_Set_Input>;
	/** filter the rows which have to be updated */
	where: Webhook_Logs_Bool_Exp;
};

/** aggregate var_pop on columns */
export type Webhook_Logs_Var_Pop_Fields = {
	__typename?: 'webhook_logs_var_pop_fields';
	retry_count?: Maybe<Scalars['Float']['output']>;
};

/** aggregate var_samp on columns */
export type Webhook_Logs_Var_Samp_Fields = {
	__typename?: 'webhook_logs_var_samp_fields';
	retry_count?: Maybe<Scalars['Float']['output']>;
};

/** aggregate variance on columns */
export type Webhook_Logs_Variance_Fields = {
	__typename?: 'webhook_logs_variance_fields';
	retry_count?: Maybe<Scalars['Float']['output']>;
};

export type InsertOrganizationMutationVariables = Exact<{
	organization: Organizations_Insert_Input;
}>;

export type InsertOrganizationMutation = {
	__typename?: 'mutation_root';
	insert_organizations_one?: {
		__typename?: 'organizations';
		id: string;
		logto_org_id: string;
		name: string;
		slug: string;
		description?: string | null;
		website_url?: string | null;
		email_domain?: string | null;
		organization_type?: string | null;
		settings?: any | null;
		is_active?: boolean | null;
		created_at?: string | null;
		updated_at?: string | null;
	} | null;
};

export type UpdateOrganizationMutationVariables = Exact<{
	logtoOrgId: Scalars['uuid']['input'];
	changes: Organizations_Set_Input;
}>;

export type UpdateOrganizationMutation = {
	__typename?: 'mutation_root';
	update_organizations?: {
		__typename?: 'organizations_mutation_response';
		affected_rows: number;
		returning: Array<{
			__typename?: 'organizations';
			id: string;
			logto_org_id: string;
			name: string;
			slug: string;
			description?: string | null;
			website_url?: string | null;
			email_domain?: string | null;
			organization_type?: string | null;
			settings?: any | null;
			is_active?: boolean | null;
			updated_at?: string | null;
		}>;
	} | null;
};

export type InsertUserOrganizationMutationVariables = Exact<{
	membership: User_Organizations_Insert_Input;
}>;

export type InsertUserOrganizationMutation = {
	__typename?: 'mutation_root';
	insert_user_organizations_one?: {
		__typename?: 'user_organizations';
		id: string;
		user_id?: string | null;
		organization_id?: string | null;
		role: string;
		status?: string | null;
		invited_by?: string | null;
		invited_at?: string | null;
		joined_at?: string | null;
		created_at?: string | null;
		updated_at?: string | null;
	} | null;
};

export type UpdateUserOrganizationRoleMutationVariables = Exact<{
	userId: Scalars['uuid']['input'];
	organizationId: Scalars['uuid']['input'];
	changes: User_Organizations_Set_Input;
}>;

export type UpdateUserOrganizationRoleMutation = {
	__typename?: 'mutation_root';
	update_user_organizations?: {
		__typename?: 'user_organizations_mutation_response';
		affected_rows: number;
		returning: Array<{
			__typename?: 'user_organizations';
			id: string;
			user_id?: string | null;
			organization_id?: string | null;
			role: string;
			status?: string | null;
			updated_at?: string | null;
		}>;
	} | null;
};

export type GetOrganizationByLogtoIdQueryVariables = Exact<{
	logtoOrgId: Scalars['uuid']['input'];
}>;

export type GetOrganizationByLogtoIdQuery = {
	__typename?: 'query_root';
	organizations: Array<{
		__typename?: 'organizations';
		id: string;
		logto_org_id: string;
		name: string;
		slug: string;
		description?: string | null;
		website_url?: string | null;
		email_domain?: string | null;
		organization_type?: string | null;
		settings?: any | null;
		is_active?: boolean | null;
		created_at?: string | null;
		updated_at?: string | null;
	}>;
};

export type GetUserOrganizationsQueryVariables = Exact<{
	userId: Scalars['uuid']['input'];
}>;

export type GetUserOrganizationsQuery = {
	__typename?: 'query_root';
	user_organizations: Array<{
		__typename?: 'user_organizations';
		id: string;
		role: string;
		status?: string | null;
		joined_at?: string | null;
		organization?: {
			__typename?: 'organizations';
			id: string;
			name: string;
			slug: string;
			description?: string | null;
			organization_type?: string | null;
			is_active?: boolean | null;
		} | null;
	}>;
};

export type InsertUserMutationVariables = Exact<{
	user: Users_Insert_Input;
}>;

export type InsertUserMutation = {
	__typename?: 'mutation_root';
	insert_users_one?: {
		__typename?: 'users';
		id: string;
		logto_user_id: string;
		email: string;
		username?: string | null;
		first_name?: string | null;
		last_name?: string | null;
		phone?: string | null;
		avatar_url?: string | null;
		email_verified?: boolean | null;
		is_active?: boolean | null;
		created_at?: string | null;
		updated_at?: string | null;
	} | null;
};

export type UpdateUserMutationVariables = Exact<{
	logtoUserId: Scalars['String']['input'];
	changes: Users_Set_Input;
}>;

export type UpdateUserMutation = {
	__typename?: 'mutation_root';
	update_users?: {
		__typename?: 'users_mutation_response';
		affected_rows: number;
		returning: Array<{
			__typename?: 'users';
			id: string;
			logto_user_id: string;
			email: string;
			username?: string | null;
			first_name?: string | null;
			last_name?: string | null;
			phone?: string | null;
			avatar_url?: string | null;
			email_verified?: boolean | null;
			is_active?: boolean | null;
			last_login_at?: string | null;
			updated_at?: string | null;
		}>;
	} | null;
};

export type UpsertUserMutationVariables = Exact<{
	user: Users_Insert_Input;
}>;

export type UpsertUserMutation = {
	__typename?: 'mutation_root';
	insert_users_one?: {
		__typename?: 'users';
		id: string;
		logto_user_id: string;
		email: string;
		username?: string | null;
		first_name?: string | null;
		last_name?: string | null;
		phone?: string | null;
		avatar_url?: string | null;
		email_verified?: boolean | null;
		is_active?: boolean | null;
		last_login_at?: string | null;
		created_at?: string | null;
		updated_at?: string | null;
	} | null;
};

export type GetUserByLogtoIdQueryVariables = Exact<{
	logtoUserId: Scalars['String']['input'];
}>;

export type GetUserByLogtoIdQuery = {
	__typename?: 'query_root';
	users: Array<{
		__typename?: 'users';
		id: string;
		logto_user_id: string;
		email: string;
		username?: string | null;
		first_name?: string | null;
		last_name?: string | null;
		phone?: string | null;
		avatar_url?: string | null;
		email_verified?: boolean | null;
		is_active?: boolean | null;
		last_login_at?: string | null;
		created_at?: string | null;
		updated_at?: string | null;
	}>;
};

export type LogWebhookEventMutationVariables = Exact<{
	log: Webhook_Logs_Insert_Input;
}>;

export type LogWebhookEventMutation = {
	__typename?: 'mutation_root';
	insert_webhook_logs_one?: {
		__typename?: 'webhook_logs';
		id: string;
		webhook_type: string;
		logto_event_id?: string | null;
		payload: any;
		processing_status?: string | null;
		error_message?: string | null;
		processed_at?: string | null;
		retry_count?: number | null;
		created_at?: string | null;
	} | null;
};

export type GetWebhookLogsQueryVariables = Exact<{
	limit?: InputMaybe<Scalars['Int']['input']>;
}>;

export type GetWebhookLogsQuery = {
	__typename?: 'query_root';
	webhook_logs: Array<{
		__typename?: 'webhook_logs';
		id: string;
		webhook_type: string;
		logto_event_id?: string | null;
		processing_status?: string | null;
		error_message?: string | null;
		processed_at?: string | null;
		retry_count?: number | null;
		created_at?: string | null;
	}>;
};

export type GetUsersQueryVariables = Exact<{ [key: string]: never }>;

export type GetUsersQuery = {
	__typename?: 'query_root';
	users: Array<{
		__typename?: 'users';
		id: string;
		email: string;
		created_at?: string | null;
		updated_at?: string | null;
	}>;
};

export type GetUserByIdQueryVariables = Exact<{
	id: Scalars['uuid']['input'];
}>;

export type GetUserByIdQuery = {
	__typename?: 'query_root';
	users_by_pk?: {
		__typename?: 'users';
		id: string;
		email: string;
		created_at?: string | null;
		updated_at?: string | null;
	} | null;
};

export const InsertOrganizationDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'InsertOrganization' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'organization' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'organizations_insert_input' } }
					}
				}
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'insert_organizations_one' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'object' },
								value: { kind: 'Variable', name: { kind: 'Name', value: 'organization' } }
							}
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'logto_org_id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'slug' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'description' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'website_url' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'email_domain' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'organization_type' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'settings' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'is_active' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'created_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'updated_at' } }
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<InsertOrganizationMutation, InsertOrganizationMutationVariables>;
export const UpdateOrganizationDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'UpdateOrganization' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'logtoOrgId' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'uuid' } }
					}
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'changes' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'organizations_set_input' } }
					}
				}
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_organizations' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'where' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: 'logto_org_id' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: '_eq' },
														value: { kind: 'Variable', name: { kind: 'Name', value: 'logtoOrgId' } }
													}
												]
											}
										}
									]
								}
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: '_set' },
								value: { kind: 'Variable', name: { kind: 'Name', value: 'changes' } }
							}
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'affected_rows' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'returning' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'logto_org_id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'slug' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'description' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'website_url' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'email_domain' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'organization_type' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'settings' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'is_active' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'updated_at' } }
										]
									}
								}
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<UpdateOrganizationMutation, UpdateOrganizationMutationVariables>;
export const InsertUserOrganizationDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'InsertUserOrganization' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'membership' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'user_organizations_insert_input' }
						}
					}
				}
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'insert_user_organizations_one' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'object' },
								value: { kind: 'Variable', name: { kind: 'Name', value: 'membership' } }
							}
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'user_id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'organization_id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'role' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'status' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'invited_by' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'invited_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'joined_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'created_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'updated_at' } }
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<
	InsertUserOrganizationMutation,
	InsertUserOrganizationMutationVariables
>;
export const UpdateUserOrganizationRoleDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'UpdateUserOrganizationRole' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'userId' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'uuid' } }
					}
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'organizationId' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'uuid' } }
					}
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'changes' } },
					type: {
						kind: 'NonNullType',
						type: {
							kind: 'NamedType',
							name: { kind: 'Name', value: 'user_organizations_set_input' }
						}
					}
				}
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_user_organizations' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'where' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: '_and' },
											value: {
												kind: 'ListValue',
												values: [
													{
														kind: 'ObjectValue',
														fields: [
															{
																kind: 'ObjectField',
																name: { kind: 'Name', value: 'user_id' },
																value: {
																	kind: 'ObjectValue',
																	fields: [
																		{
																			kind: 'ObjectField',
																			name: { kind: 'Name', value: '_eq' },
																			value: {
																				kind: 'Variable',
																				name: { kind: 'Name', value: 'userId' }
																			}
																		}
																	]
																}
															}
														]
													},
													{
														kind: 'ObjectValue',
														fields: [
															{
																kind: 'ObjectField',
																name: { kind: 'Name', value: 'organization_id' },
																value: {
																	kind: 'ObjectValue',
																	fields: [
																		{
																			kind: 'ObjectField',
																			name: { kind: 'Name', value: '_eq' },
																			value: {
																				kind: 'Variable',
																				name: { kind: 'Name', value: 'organizationId' }
																			}
																		}
																	]
																}
															}
														]
													}
												]
											}
										}
									]
								}
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: '_set' },
								value: { kind: 'Variable', name: { kind: 'Name', value: 'changes' } }
							}
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'affected_rows' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'returning' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'user_id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'organization_id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'role' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'status' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'updated_at' } }
										]
									}
								}
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<
	UpdateUserOrganizationRoleMutation,
	UpdateUserOrganizationRoleMutationVariables
>;
export const GetOrganizationByLogtoIdDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'GetOrganizationByLogtoId' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'logtoOrgId' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'uuid' } }
					}
				}
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'organizations' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'where' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: 'logto_org_id' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: '_eq' },
														value: { kind: 'Variable', name: { kind: 'Name', value: 'logtoOrgId' } }
													}
												]
											}
										}
									]
								}
							}
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'logto_org_id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'slug' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'description' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'website_url' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'email_domain' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'organization_type' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'settings' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'is_active' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'created_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'updated_at' } }
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<GetOrganizationByLogtoIdQuery, GetOrganizationByLogtoIdQueryVariables>;
export const GetUserOrganizationsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'GetUserOrganizations' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'userId' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'uuid' } }
					}
				}
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'user_organizations' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'where' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: 'user_id' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: '_eq' },
														value: { kind: 'Variable', name: { kind: 'Name', value: 'userId' } }
													}
												]
											}
										}
									]
								}
							}
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'role' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'status' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'joined_at' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'organization' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'slug' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'description' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'organization_type' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'is_active' } }
										]
									}
								}
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<GetUserOrganizationsQuery, GetUserOrganizationsQueryVariables>;
export const InsertUserDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'InsertUser' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'user' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'users_insert_input' } }
					}
				}
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'insert_users_one' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'object' },
								value: { kind: 'Variable', name: { kind: 'Name', value: 'user' } }
							}
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'logto_user_id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'email' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'username' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'phone' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'avatar_url' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'email_verified' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'is_active' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'created_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'updated_at' } }
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<InsertUserMutation, InsertUserMutationVariables>;
export const UpdateUserDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'UpdateUser' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'logtoUserId' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'String' } }
					}
				},
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'changes' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'users_set_input' } }
					}
				}
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'update_users' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'where' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: 'logto_user_id' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: '_eq' },
														value: {
															kind: 'Variable',
															name: { kind: 'Name', value: 'logtoUserId' }
														}
													}
												]
											}
										}
									]
								}
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: '_set' },
								value: { kind: 'Variable', name: { kind: 'Name', value: 'changes' } }
							}
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'affected_rows' } },
								{
									kind: 'Field',
									name: { kind: 'Name', value: 'returning' },
									selectionSet: {
										kind: 'SelectionSet',
										selections: [
											{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'logto_user_id' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'email' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'username' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'phone' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'avatar_url' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'email_verified' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'is_active' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'last_login_at' } },
											{ kind: 'Field', name: { kind: 'Name', value: 'updated_at' } }
										]
									}
								}
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<UpdateUserMutation, UpdateUserMutationVariables>;
export const UpsertUserDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'UpsertUser' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'user' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'users_insert_input' } }
					}
				}
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'insert_users_one' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'object' },
								value: { kind: 'Variable', name: { kind: 'Name', value: 'user' } }
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'on_conflict' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: 'constraint' },
											value: { kind: 'EnumValue', value: 'users_logto_user_id_key' }
										},
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: 'update_columns' },
											value: {
												kind: 'ListValue',
												values: [
													{ kind: 'EnumValue', value: 'email' },
													{ kind: 'EnumValue', value: 'username' },
													{ kind: 'EnumValue', value: 'first_name' },
													{ kind: 'EnumValue', value: 'last_name' },
													{ kind: 'EnumValue', value: 'phone' },
													{ kind: 'EnumValue', value: 'avatar_url' },
													{ kind: 'EnumValue', value: 'email_verified' },
													{ kind: 'EnumValue', value: 'is_active' },
													{ kind: 'EnumValue', value: 'last_login_at' },
													{ kind: 'EnumValue', value: 'updated_at' }
												]
											}
										}
									]
								}
							}
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'logto_user_id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'email' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'username' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'phone' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'avatar_url' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'email_verified' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'is_active' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_login_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'created_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'updated_at' } }
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<UpsertUserMutation, UpsertUserMutationVariables>;
export const GetUserByLogtoIdDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'GetUserByLogtoId' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'logtoUserId' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'String' } }
					}
				}
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'users' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'where' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: 'logto_user_id' },
											value: {
												kind: 'ObjectValue',
												fields: [
													{
														kind: 'ObjectField',
														name: { kind: 'Name', value: '_eq' },
														value: {
															kind: 'Variable',
															name: { kind: 'Name', value: 'logtoUserId' }
														}
													}
												]
											}
										}
									]
								}
							}
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'logto_user_id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'email' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'username' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'first_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_name' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'phone' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'avatar_url' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'email_verified' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'is_active' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'last_login_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'created_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'updated_at' } }
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<GetUserByLogtoIdQuery, GetUserByLogtoIdQueryVariables>;
export const LogWebhookEventDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'mutation',
			name: { kind: 'Name', value: 'LogWebhookEvent' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'log' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'webhook_logs_insert_input' } }
					}
				}
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'insert_webhook_logs_one' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'object' },
								value: { kind: 'Variable', name: { kind: 'Name', value: 'log' } }
							}
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'webhook_type' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'logto_event_id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'payload' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'processing_status' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'error_message' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'processed_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'retry_count' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'created_at' } }
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<LogWebhookEventMutation, LogWebhookEventMutationVariables>;
export const GetWebhookLogsDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'GetWebhookLogs' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'limit' } },
					type: { kind: 'NamedType', name: { kind: 'Name', value: 'Int' } },
					defaultValue: { kind: 'IntValue', value: '50' }
				}
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'webhook_logs' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'order_by' },
								value: {
									kind: 'ObjectValue',
									fields: [
										{
											kind: 'ObjectField',
											name: { kind: 'Name', value: 'created_at' },
											value: { kind: 'EnumValue', value: 'desc' }
										}
									]
								}
							},
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'limit' },
								value: { kind: 'Variable', name: { kind: 'Name', value: 'limit' } }
							}
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'webhook_type' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'logto_event_id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'processing_status' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'error_message' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'processed_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'retry_count' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'created_at' } }
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<GetWebhookLogsQuery, GetWebhookLogsQueryVariables>;
export const GetUsersDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'GetUsers' },
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'users' },
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'email' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'created_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'updated_at' } }
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<GetUsersQuery, GetUsersQueryVariables>;
export const GetUserByIdDocument = {
	kind: 'Document',
	definitions: [
		{
			kind: 'OperationDefinition',
			operation: 'query',
			name: { kind: 'Name', value: 'GetUserById' },
			variableDefinitions: [
				{
					kind: 'VariableDefinition',
					variable: { kind: 'Variable', name: { kind: 'Name', value: 'id' } },
					type: {
						kind: 'NonNullType',
						type: { kind: 'NamedType', name: { kind: 'Name', value: 'uuid' } }
					}
				}
			],
			selectionSet: {
				kind: 'SelectionSet',
				selections: [
					{
						kind: 'Field',
						name: { kind: 'Name', value: 'users_by_pk' },
						arguments: [
							{
								kind: 'Argument',
								name: { kind: 'Name', value: 'id' },
								value: { kind: 'Variable', name: { kind: 'Name', value: 'id' } }
							}
						],
						selectionSet: {
							kind: 'SelectionSet',
							selections: [
								{ kind: 'Field', name: { kind: 'Name', value: 'id' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'email' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'created_at' } },
								{ kind: 'Field', name: { kind: 'Name', value: 'updated_at' } }
							]
						}
					}
				]
			}
		}
	]
} as unknown as DocumentNode<GetUserByIdQuery, GetUserByIdQueryVariables>;
