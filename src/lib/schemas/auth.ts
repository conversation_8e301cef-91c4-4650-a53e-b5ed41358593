import { z } from 'zod';

// Password validation schema
const passwordSchema = z
	.string()
	.min(8, 'Password must be at least 8 characters')
	.regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
	.regex(/[a-z]/, 'Password must contain at least one lowercase letter')
	.regex(/[0-9]/, 'Password must contain at least one number');

// Sign-in schema
export const signInSchema = z.object({
	email: z.string().email('Please enter a valid email address'),
	password: passwordSchema,
	rememberMe: z.boolean().default(false)
});

// Registration schema - simplified without name fields and confirm password
export const registerSchema = z.object({
	email: z.string().email('Please enter a valid email address'),
	password: passwordSchema,
	acceptTerms: z.boolean().refine(val => val === true, {
		message: 'You must accept the terms and conditions'
	})
});

export type SignInForm = z.infer<typeof signInSchema>;
export type RegisterForm = z.infer<typeof registerSchema>;