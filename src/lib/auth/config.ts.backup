import type { LogtoConfig } from '@logto/sveltekit';
import { UserScope } from '@logto/sveltekit';
import { env } from '$env/dynamic/private';

console.log('Logto config loading with env:', {
	endpoint: env.LOGTO_ENDPOINT,
	appId: env.LOGTO_APP_ID ? 'SET' : 'NOT SET',
	appSecret: env.LOGTO_APP_SECRET ? 'SET' : 'NOT SET',
	cookieSecret: env.LOGTO_COOKIE_ENCRYPTION_KEY ? 'SET' : 'NOT SET'
});

export const logtoConfig: LogtoConfig = {
	endpoint: env.LOGTO_ENDPOINT!,
	appId: env.LOGTO_APP_ID!,
	appSecret: env.LOGTO_APP_SECRET!,
	// Request user profile and organization info
	scopes: [
		UserScope.Email,
		UserScope.Profile,
		UserScope.Organizations,
		UserScope.OrganizationRoles,
		'urn:logto:scope:organization_roles'
	],
	// Additional resources for JWT claims
	resources: [
		'https://default.logto.app/api',
		'urn:logto:resource:organization'
	]
};
