<!-- Example: Simple Form Pattern -->
<script lang="ts">
  import { enhance } from '$app/forms';
  
  let name = '';
  let email = '';
  let category = '';
  let description = '';
</script>

<div class="container mx-auto px-4 py-8 max-w-2xl">
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h1 class="text-2xl font-bold text-gray-900 mb-6">Contact Form</h1>
    
    <form method="POST" use:enhance class="space-y-4">
      <!-- Name Field -->
      <div class="mb-4">
        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
        <input
          id="name"
          name="name"
          type="text"
          bind:value={name}
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Enter your full name"
          required
        />
      </div>

      <!-- Email Field -->
      <div class="mb-4">
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
        <input
          id="email"
          name="email"
          type="email"
          bind:value={email}
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Enter your email address"
          required
        />
      </div>

      <!-- Category Field -->
      <div class="mb-4">
        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
        <select
          id="category"
          name="category"
          bind:value={category}
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          required
        >
          <option value="">Choose a category</option>
          <option value="general">General Inquiry</option>
          <option value="support">Technical Support</option>
          <option value="billing">Billing Question</option>
          <option value="feedback">Feedback</option>
        </select>
      </div>

      <!-- Description Field (Optional) -->
      <div class="mb-4">
        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Message (Optional)</label>
        <textarea
          id="description"
          name="description"
          bind:value={description}
          rows="4"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Tell us more about your inquiry..."
        ></textarea>
      </div>

      <!-- Form Actions -->
      <div class="flex gap-4 pt-4">
        <button type="submit" class="flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          Send Message
        </button>
        <button type="button" class="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" on:click={() => history.back()}>
          Cancel
        </button>
      </div>
    </form>
  </div>
</div>
