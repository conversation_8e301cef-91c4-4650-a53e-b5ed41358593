# SourceFlex Design System

## Overview
SourceFlex uses a consistent design system built on **Bits UI** (headless components) + **Tailwind CSS** for styling. This ensures consistency across all pages and components.

## Core Principles

### 1. **Component Architecture**
- **Headless UI**: Bits UI provides accessibility and behavior
- **Custom Styling**: Tailwind CSS for consistent visual design
- **Modular**: Each component is self-contained with its own variants
- **Reusable**: Components can be used across the entire application

### 2. **Design Tokens**

#### Colors
```typescript
const colors = {
  primary: 'blue-600',    // Main brand color
  secondary: 'gray-600',  // Secondary actions
  success: 'green-600',   // Success states
  warning: 'yellow-600',  // Warning states
  danger: 'red-600',      // Error/danger states
  neutral: 'gray-300'     // Borders, disabled states
};
```

#### Typography
```typescript
const typography = {
  headings: 'font-bold text-gray-900',
  body: 'text-gray-700',
  captions: 'text-sm text-gray-600',
  labels: 'text-sm font-medium text-gray-700'
};
```

#### Spacing
- Uses Tailwind's standard spacing scale (4px base unit)
- Components use consistent padding/margin patterns
- Form elements have standardized spacing

### 3. **Component Library**

#### Button Component
```svelte
<Button variant="primary" size="md" fullWidth>
  Sign In
</Button>
```

**Variants:**
- `primary` - Blue background, white text
- `secondary` - Gray background, white text  
- `outline` - White background, gray border
- `ghost` - Transparent background
- `danger` - Red background, white text

**Sizes:**
- `sm` - Small padding and text
- `md` - Default size
- `lg` - Large padding and text

#### Input Component
```svelte
<Input 
  label="Email Address"
  type="email" 
  bind:value={email}
  placeholder="Enter email"
  helperText="We'll never share your email"
  error="Invalid email format"
  required
/>
```

#### Checkbox Component
```svelte
<Checkbox bind:checked={accepted}>
  I agree to the terms
</Checkbox>
```

#### Alert Component
```svelte
<Alert variant="error" title="Error">
  Something went wrong!
</Alert>
```

**Variants:**
- `info` - Blue theme
- `success` - Green theme
- `warning` - Yellow theme  
- `error` - Red theme

## Usage Guidelines

### 1. **Import Pattern**
```typescript
import { Button, Input, Checkbox, Alert } from '$lib/components/ui';
```

### 2. **Consistency Rules**

#### Forms
- Always use the `Input` component for form fields
- Use consistent spacing between form elements (`space-y-6`)
- Include proper labels and error states
- Use `Button` component for form submissions

#### Error Handling
- Use `Alert` component for form-level errors
- Use `error` prop on `Input` for field-level errors
- Consistent error messaging patterns

#### Loading States
- Use `loading` prop on buttons during async operations
- Disable form elements during loading
- Show appropriate loading text

### 3. **Page Layout Standards**

#### Auth Pages
```svelte
<div class="container mx-auto px-4 py-8 max-w-md">
  <div class="bg-white p-8 rounded-lg shadow-md">
    <!-- Content -->
  </div>
</div>
```

#### Dashboard Pages
```svelte
<div class="container mx-auto px-4 py-8">
  <!-- Content with responsive grid -->
</div>
```

## Extending the System

### Adding New Components

1. **Create in `/lib/components/ui/`**
2. **Use Bits UI primitives when possible**
3. **Follow existing patterns for props and styling**
4. **Export from `/lib/components/ui/index.ts`**
5. **Document variants and usage**

### Example New Component
```svelte
<!-- Modal.svelte -->
<script lang="ts">
  import { Dialog } from 'bits-ui';
  import { cn } from '$lib/utils/cn';
  
  // Component implementation
</script>
```

## Benefits

### 1. **Consistency**
- All components follow the same design patterns
- Colors, typography, and spacing are standardized
- User experience is predictable across the app

### 2. **Maintainability**  
- Changes to design tokens update entire app
- Components are reusable and well-tested
- Easy to add new variants or modify existing ones

### 3. **Accessibility**
- Bits UI provides ARIA support out of the box
- Proper focus management and keyboard navigation
- Screen reader friendly

### 4. **Developer Experience**
- TypeScript support for all components
- Clear props interface and validation
- Consistent API across all components

### 5. **Performance**
- Tree-shakeable components
- Minimal bundle size impact
- Optimized for Svelte 5 runes

## Migration from Flowbite

The new design system replaces Flowbite-svelte with:
- Better TypeScript support
- Svelte 5 runes compatibility  
- More customizable styling
- Consistent design tokens
- Improved accessibility
