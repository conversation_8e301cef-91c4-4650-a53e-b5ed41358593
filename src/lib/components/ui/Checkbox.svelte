<script lang="ts">
	import { cn } from '$lib/utils/cn';
	import type { Snippet } from 'svelte';

	interface Props {
		checked?: boolean;
		disabled?: boolean;
		required?: boolean;
		name?: string;
		id?: string;
		value?: string;
		class?: string;
		children?: Snippet;
	}

	let {
		checked = $bindable(false),
		disabled = false,
		required = false,
		name,
		id,
		value,
		class: className,
		children,
		...restProps
	}: Props = $props();

	const inputId = id || name || `checkbox-${Math.random().toString(36).substr(2, 9)}`;
</script>

<div class={cn('flex items-center space-x-2', className)} {...restProps}>
	<input
		{id}
		{name}
		{value}
		{required}
		{disabled}
		type="checkbox"
		bind:checked
		class="h-4 w-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
	/>
	{#if children}
		<label for={inputId} class="text-sm text-gray-900 select-none">
			{@render children()}
		</label>
	{/if}
</div>
