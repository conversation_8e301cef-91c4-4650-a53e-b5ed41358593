<script lang="ts">
	import { Button } from 'bits-ui';

	export let variant: 'primary' | 'secondary' | 'danger' = 'primary';
	export let size: 'small' | 'medium' | 'large' = 'medium';
	export let disabled = false;

	$: buttonClass = [
		// Base styles
		'font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-opacity-20',
		
		// Size variants
		size === 'small' 
			? 'h-8 px-3 text-sm'
			: size === 'large'
			? 'h-12 px-6 text-lg'
			: 'h-10 px-4 text-base',
		
		// Border radius
		'rounded-md',
		
		// Variant styles
		variant === 'primary'
			? 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500'
			: variant === 'danger'
			? 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500'
			: 'border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-blue-500',
		
		// Disabled state
		disabled 
			? 'opacity-50 cursor-not-allowed'
			: 'cursor-pointer'
	].join(' ');
</script>

<Button.Root 
	class={buttonClass}
	{disabled}
	{...$$restProps}
>
	<slot />
</Button.Root>
