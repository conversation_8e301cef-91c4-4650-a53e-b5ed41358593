<script lang="ts">
	import { cn } from "$lib/utils/cn";
	import type { Snippet } from "svelte";

	let {
		children,
		class: className,
		as: Tag = "div",
		duration = "2s",
		borderWidth = "2px",
		containerClassName,
		borderClassName,
		...props
	}: {
		children: Snippet;
		class?: string;
		as?: keyof HTMLElementTagNameMap;
		duration?: string;
		borderWidth?: string;
		containerClassName?: string;
		borderClassName?: string;
		[key: string]: any;
	} = $props();
</script>

<svelte:element
	this={Tag}
	class={cn(
		"bg-transparent relative h-full w-full p-[1px] overflow-hidden",
		containerClassName
	)}
	{...props}
>
	<div
		class="absolute inset-0"
		style:--duration={duration}
		style:--border-width={borderWidth}
	>
		<div
			class={cn(
				"absolute inset-[var(--border-width)] rounded-[inherit] [border:calc(var(--border-width)/2)_solid_transparent]",
				"animate-moving-border-spin",
				borderClassName
			)}
		/>
	</div>
	<svelte:element
		this={Tag}
		class={cn(
			"w-full h-full bg-background text-foreground rounded-[inherit]",
			className
		)}
	>
		{@render children()}
	</svelte:element>
</svelte:element>
