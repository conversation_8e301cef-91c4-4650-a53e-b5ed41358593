<!--
  @component
  A component that creates a moving border effect.

  Usage:
  ```svelte
  <MovingBorder duration={2000} rx="30%" ry="30%">
    <div class="h-20 w-20 opacity-[0.8] bg-[radial-gradient(var(--sky-500)_40%,transparent_60%)]"/>
  </MovingBorder>
  ```
-->
<script lang="ts">
  let {
    duration = 2000,
    rx: rxProp,
    ry: ryProp,
    children,
    ...rest
  } = $props<{
    duration?: number;
    rx?: string;
    ry?: string;
    children?: import('svelte').Snippet;
    [key: string]: any;
  }>();

  // Use runes for reactivity
  let pathRef: SVGPathElement | null = $state(null);
  let progress = $state(0);
  let svgRef: SVGSVGElement | null = $state(null);
  let d = $state('');

  // Animation values
  let x = $state(0);
  let y = $state(0);

  $effect(() => {
    if (!svgRef) return;
    const { width, height } = svgRef.getBoundingClientRect();

    const getPixels = (value: string | undefined, total: number) => {
      if (!value) return 0;
      if (value.endsWith('%')) {
        return (parseFloat(value) / 100) * total;
      }
      return parseFloat(value);
    };

    const rx = getPixels(rxProp, width);
    const ry = getPixels(ryProp, height);

    if (width > 0 && height > 0) {
      d = `M${rx},0 H${width - rx} A${rx},${ry} 0 0 1 ${width},${ry} V${
        height - ry
      } A${rx},${ry} 0 0 1 ${width - rx},${height} H${rx} A${rx},${ry} 0 0 1 0,${
        height - ry
      } V${ry} A${rx},${ry} 0 0 1 ${rx},0 Z`;
    }
  });

  // Update position based on animation progress
  $effect(() => {
    if (!pathRef || !pathRef.getTotalLength()) return;

    const point = pathRef.getPointAtLength(progress);
    if (point) {
      x = point.x;
      y = point.y;
    }
  });

  // Animation frame effect
  $effect(() => {
    let animationFrameId: number;
    let startTime: number | undefined;

    const animate = (timestamp: number) => {
      if (startTime === undefined) {
        startTime = timestamp;
      }
      const time = timestamp - startTime;

      if (!pathRef) return;

      const length = pathRef.getTotalLength();
      if (length) {
        const pxPerMillisecond = length / duration;
        progress = (time * pxPerMillisecond) % length;
      }
      animationFrameId = requestAnimationFrame(animate);
    };

    animationFrameId = requestAnimationFrame(animate);

    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  });

  // Create transform string
  let transformStyle = $derived(
    `translateX(${x}px) translateY(${y}px) translateX(-50%) translateY(-50%)`
  );
</script>

<div class="relative w-full h-full">
  <svg
    bind:this={svgRef}
    xmlns="http://www.w3.org/2000/svg"
    preserveAspectRatio="none"
    class="absolute h-full w-full"
    width="100%"
    height="100%"
    {...rest}
  >
    <path bind:this={pathRef} d={d} fill="none" />
  </svg>

  <div
    style:position="absolute"
    style:top="0"
    style:left="0"
    style:display="inline-block"
    style:transform={transformStyle}
  >
    {@render children()}
  </div>
</div>
