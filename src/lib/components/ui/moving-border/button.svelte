<!--
  @component
  A button component with a moving border effect.

  Usage:
  ```svelte
  <Button
    borderRadius="1.75rem"
    class="bg-white dark:bg-slate-900 text-black dark:text-white border-neutral-200 dark:border-slate-800"
  >
    Borders are cool
  </Button>
  ```
-->
<script lang="ts">
  import { cn } from '$lib/utils';
  import MovingBorder from './moving-border.svelte';
  
  let {
    borderRadius = "1.75rem",
    containerClass,
    borderClass,
    duration,
    className,
    children,
    ...rest
  } = $props<{
    borderRadius?: string;
    containerClass?: string;
    borderClass?: string;
    duration?: number;
    className?: string;
    children?: import('svelte').Snippet;
    [key: string]: any;
  }>();
  
  // Computed border radius for inner elements
  let innerBorderRadius = $derived(`calc(${borderRadius} * 0.96)`);
</script>

<button
  class={cn(
    "bg-transparent relative text-xl h-16 w-40 p-[1px] overflow-hidden",
    containerClass
  )}
  style:border-radius={borderRadius}
  {...rest}
>
  <div
    class="absolute inset-0"
    style:border-radius={innerBorderRadius}
  >
    <MovingBorder duration={duration} rx="30%" ry="30%">
      <div
        class={cn(
          "h-20 w-20 opacity-[0.8] bg-[radial-gradient(var(--sky-500)_40%,transparent_60%)]",
          borderClass
        )}
      />
    </MovingBorder>
  </div>
  
  <div
    class={cn(
      "relative bg-slate-900/[0.8] border border-slate-800 backdrop-blur-xl text-white flex items-center justify-center w-full h-full text-sm antialiased",
      className
    )}
    style:border-radius={innerBorderRadius}
  >
    {@render children?.()}
  </div>
</button>
