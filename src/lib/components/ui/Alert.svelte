<script lang="ts">
	import { cn } from '$lib/utils/cn';
	import type { Snippet } from 'svelte';

	interface Props {
		variant?: 'info' | 'success' | 'warning' | 'error';
		title?: string;
		children: Snippet;
		class?: string;
	}

	let {
		variant = 'info',
		title,
		children,
		class: className,
		...restProps
	}: Props = $props();

	const variants = {
		info: 'bg-blue-50 border-blue-200 text-blue-800',
		success: 'bg-green-50 border-green-200 text-green-800',
		warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
		error: 'bg-red-50 border-red-200 text-red-800'
	};

	const iconVariants = {
		info: 'text-blue-400',
		success: 'text-green-400',
		warning: 'text-yellow-400',
		error: 'text-red-400'
	};
</script>

<div class={cn('border px-4 py-3 rounded-md', variants[variant], className)} {...restProps}>
	<div class="flex">
		<div class="flex-shrink-0">
			{#if variant === 'info'}
				<svg class="h-5 w-5 {iconVariants[variant]}" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
				</svg>
			{:else if variant === 'success'}
				<svg class="h-5 w-5 {iconVariants[variant]}" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
				</svg>
			{:else if variant === 'warning'}
				<svg class="h-5 w-5 {iconVariants[variant]}" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
				</svg>
			{:else if variant === 'error'}
				<svg class="h-5 w-5 {iconVariants[variant]}" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
				</svg>
			{/if}
		</div>
		<div class="ml-3">
			{#if title}
				<h3 class="text-sm font-medium">{title}</h3>
				<div class="mt-2 text-sm">
					{@render children()}
				</div>
			{:else}
				<div class="text-sm">
					{@render children()}
				</div>
			{/if}
		</div>
	</div>
</div>
