<script lang="ts">
	import { cn } from '$lib/utils/cn';
	import type { Snippet } from 'svelte';

	interface Props {
		label?: string;
		error?: string;
		helperText?: string;
		required?: boolean;
		disabled?: boolean;
		type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
		placeholder?: string;
		value?: string;
		name?: string;
		id?: string;
		autocomplete?: string;
		class?: string;
	}

	let {
		label,
		error,
		helperText,
		required = false,
		disabled = false,
		type = 'text',
		placeholder,
		value = $bindable(''),
		name,
		id,
		autocomplete,
		class: className,
		...restProps
	}: Props = $props();

	const inputId = id || name || `input-${Math.random().toString(36).substr(2, 9)}`;
</script>

<div class="space-y-2">
	{#if label}
		<label for={inputId} class="block text-sm font-medium text-gray-700">
			{label}
			{#if required}
				<span class="text-red-500">*</span>
			{/if}
		</label>
	{/if}
	
	<input
		{id}
		{name}
		{type}
		{placeholder}
		{autocomplete}
		{required}
		{disabled}
		bind:value
		class={cn(
			'w-full px-3 py-2 border rounded-md shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-offset-1',
			error
				? 'border-red-300 focus:border-red-500 focus:ring-red-500'
				: 'border-gray-300 focus:border-blue-500 focus:ring-blue-500',
			disabled && 'bg-gray-50 cursor-not-allowed',
			className
		)}
		{...restProps}
	/>
	
	{#if error}
		<p class="text-sm text-red-600">{error}</p>
	{:else if helperText}
		<p class="text-sm text-gray-600">{helperText}</p>
	{/if}
</div>
