<script lang="ts">
	import { Motion } from "svelte-motion";
	import { cn } from "$lib/utils/cn";
	import type { ComponentType } from "svelte";

	let {
		icon,
		label,
		onclick = () => {},
		class: className = ""
	}: {
		icon: ComponentType;
		label: string;
		onclick?: () => void;
		class?: string;
	} = $props();

	const Icon = icon;
	let isPressed = $state(false);
	let isMobile = $state(false);
	let showTooltip = $state(false);

	// Detect mobile device - only show tooltips on desktop
	$effect(() => {
		if (typeof window !== 'undefined') {
			isMobile = window.matchMedia('(pointer: coarse)').matches;
		}
	});

	// Handle touch events for mobile feedback (no tooltip)
	function handleTouchStart() {
		isPressed = true;
	}

	function handleTouchEnd() {
		isPressed = false;
	}

	// Handle mouse events for desktop only (with tooltip)
	function handleMouseEnter() {
		if (!isMobile) {
			showTooltip = true;
		}
	}

	function handleMouseLeave() {
		if (!isMobile) {
			showTooltip = false;
		}
	}

	// Handle click
	function handleClick() {
		if (onclick) {
			onclick();
		}
	}

	// Create derived hover behavior - only for desktop
	let hoverBehavior = $derived(isMobile ? {} : { scale: 1.1, y: -2 });
</script>

<div class="relative">
	<Motion
		let:motion
		whileHover={hoverBehavior}
		whileTap={{ scale: 0.95 }}
		animate={isPressed ? { scale: 0.95 } : { scale: 1 }}
	>
		<button
			use:motion
			onclick={handleClick}
			ontouchstart={handleTouchStart}
			ontouchend={handleTouchEnd}
			ontouchcancel={handleTouchEnd}
			onmouseenter={handleMouseEnter}
			onmouseleave={handleMouseLeave}
			class={cn(
				"relative group p-3 rounded-lg",
				"hover:bg-secondary active:bg-secondary/80",
				"transition-colors duration-200",
				"focus-visible:outline-none focus-visible:ring-2", 
				"focus-visible:ring-ring focus-visible:ring-offset-2",
				"disabled:pointer-events-none disabled:opacity-50",
				className
			)}
			aria-label={label}
		>
			<Icon class="w-5 h-5 text-foreground" />
		</button>
	</Motion>
	
	<!-- Tooltip only for desktop (non-touch devices) -->
	{#if showTooltip && !isMobile}
		<div
			class={cn(
				"absolute -top-10 left-1/2 -translate-x-1/2",
				"px-3 py-1.5 rounded-md text-xs",
				"bg-primary text-primary-foreground",
				"border shadow-md z-50",
				"whitespace-nowrap pointer-events-none",
				"animate-in fade-in-0 zoom-in-95 duration-200"
			)}
		>
			{label}
			<!-- Tooltip arrow -->
			<div class="absolute top-full left-1/2 -translate-x-1/2 border-4 border-transparent border-t-primary"></div>
		</div>
	{/if}
</div>
