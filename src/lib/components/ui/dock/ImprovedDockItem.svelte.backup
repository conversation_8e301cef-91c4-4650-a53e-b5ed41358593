<script lang="ts">
	import { Motion } from "svelte-motion";
	import { cn } from "$lib/utils/cn";
	import type { ComponentType } from "svelte";

	let {
		icon,
		label,
		onclick = () => {},
		class: className = ""
	}: {
		icon: ComponentType;
		label: string;
		onclick?: () => void;
		class?: string;
	} = $props();

	const Icon = icon;
</script>

<Motion
	let:motion
	whileHover={{ scale: 1.1, y: -2 }}
	whileTap={{ scale: 0.95 }}
>
	<button
		use:motion
		onclick={onclick}
		class={cn(
			"relative group p-3 rounded-lg",
			"hover:bg-secondary transition-colors",
			className
		)}
		aria-label={label}
	>
		<Icon class="w-5 h-5 text-foreground" />
		<span
			class={cn(
				"absolute -top-8 left-1/2 -translate-x-1/2",
				"px-2 py-1 rounded text-xs",
				"bg-popover text-popover-foreground",
				"opacity-0 group-hover:opacity-100 z-50",
				"transition-opacity whitespace-nowrap pointer-events-none"
			)}
		>
			{label}
		</span>
	</button>
</Motion>
