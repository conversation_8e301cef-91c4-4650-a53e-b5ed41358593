<script lang="ts">
	import { Motion } from "svelte-motion";
	import { cn } from "$lib/utils/cn";
	import { onMount } from "svelte";
	import type { Snippet } from "svelte";

	let {
		children,
		duration = 2000,
		rx = "30%",
		ry = "30%",
		class: className = ""
	}: {
		children: Snippet;
		duration?: number;
		rx?: string;
		ry?: string;
		class?: string;
	} = $props();

	let pathRef: SVGRectElement;
	let progress = $state(0);
	let animationId: number;

	onMount(() => {
		const animate = () => {
			if (pathRef) {
				const length = pathRef.getTotalLength();
				if (length) {
					const pxPerMillisecond = length / duration;
					progress = ((Date.now() * pxPerMillisecond) % length) / length;
				}
			}
			animationId = requestAnimationFrame(animate);
		};
		
		animationId = requestAnimationFrame(animate);
		
		return () => {
			if (animationId) {
				cancelAnimationFrame(animationId);
			}
		};
	});

	let x = $derived(0);
	let y = $derived(0);

	$effect(() => {
		if (pathRef) {
			const length = pathRef.getTotalLength();
			const point = pathRef.getPointAtLength(progress * length);
			x = point.x;
			y = point.y;
		}
	});
</script>

<div class={cn("relative", className)}>
	<svg
		xmlns="http://www.w3.org/2000/svg"
		preserveAspectRatio="none"
		class="absolute h-full w-full"
		width="100%"
		height="100%"
	>
		<rect
			fill="none"
			width="100%"
			height="100%"
			rx={rx}
			ry={ry}
			bind:this={pathRef}
		/>
	</svg>
	
	<Motion
		let:motion
		style={{
			position: "absolute",
			top: 0,
			left: 0,
			transform: `translateX(${x}px) translateY(${y}px) translateX(-50%) translateY(-50%)`
		}}
	>
		<div use:motion>
			{@render children()}
		</div>
	</Motion>
</div>
