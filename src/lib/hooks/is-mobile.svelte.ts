import { browser } from '$app/environment';

/**
 * Hook to detect if the current device is mobile with optimized performance
 * Supports multiple breakpoints and device detection methods
 */
export class IsMobile {
	private breakpoint: number;
	private mobile = $state(false);
	private resizeTimeout: number | null = null;
	private destroyed = false;
	private mediaQuery: MediaQueryList | null = null;

	constructor(breakpoint = 768) {
		this.breakpoint = breakpoint;

		if (browser) {
			this.setupMediaQuery();
			this.checkMobile();
		}
	}

	private setupMediaQuery() {
		if (!browser) return;

		// Use modern matchMedia API for better performance
		this.mediaQuery = window.matchMedia(`(max-width: ${this.breakpoint - 1}px)`);
		this.mediaQuery.addEventListener('change', this.handleMediaQueryChange);

		// Fallback to resize listener for older browsers
		window.addEventListener('resize', this.debouncedCheckMobile, { passive: true });
	}

	private handleMediaQueryChange = (e: MediaQueryListEvent) => {
		if (!this.destroyed) {
			this.mobile = e.matches;
		}
	};

	private checkMobile = () => {
		if (browser && !this.destroyed) {
			// Use matchMedia if available, fallback to window width
			const isMobile = this.mediaQuery
				? this.mediaQuery.matches
				: window.innerWidth < this.breakpoint;

			// Only update if state actually changed to prevent unnecessary re-renders
			if (this.mobile !== isMobile) {
				this.mobile = isMobile;
			}
		}
	};

	// Debounced resize handler to improve performance
	private debouncedCheckMobile = () => {
		if (this.resizeTimeout) {
			clearTimeout(this.resizeTimeout);
		}
		this.resizeTimeout = window.setTimeout(this.checkMobile, 100);
	};

	get current() {
		return this.mobile;
	}

	// Check if device is likely touch-enabled
	get isTouch() {
		if (!browser) return false;
		return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
	}

	// Get current viewport width
	get viewportWidth() {
		if (!browser) return 0;
		return window.innerWidth;
	}

	destroy() {
		this.destroyed = true;
		if (browser) {
			if (this.mediaQuery) {
				this.mediaQuery.removeEventListener('change', this.handleMediaQueryChange);
			}
			window.removeEventListener('resize', this.debouncedCheckMobile);
			if (this.resizeTimeout) {
				clearTimeout(this.resizeTimeout);
				this.resizeTimeout = null;
			}
		}
	}
}