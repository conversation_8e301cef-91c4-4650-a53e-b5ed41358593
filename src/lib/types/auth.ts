import type { UserInfoResponse } from '@logto/sveltekit';

// Extended user type for our application
export interface AppUser {
	id: string;
	email?: string;
	username?: string;
	name?: string;
	avatar?: string;
	primaryEmail?: string;
	lastSignInAt?: number;
	createdAt: number;
	updatedAt?: number;
}

// Type guard to ensure user has required fields
export function isValidUser(user: UserInfoResponse | undefined): user is UserInfoResponse & { id: string } {
	return user !== undefined && typeof user.sub === 'string' && user.sub.length > 0;
}

// Convert Logto UserInfoResponse to our AppUser type
export function toAppUser(user: UserInfoResponse): AppUser {
	return {
		id: user.sub as string,
		email: user.email || undefined,
		username: user.username || undefined,
		name: user.name || undefined,
		avatar: user.picture || undefined,
		primaryEmail: user.email || undefined,
		lastSignInAt: user.updated_at ? Math.floor(new Date(user.updated_at).getTime()) : undefined,
		createdAt: user.created_at ? Math.floor(new Date(user.created_at).getTime()) : Date.now(),
		updatedAt: user.updated_at ? Math.floor(new Date(user.updated_at).getTime()) : undefined
	};
}

// Type guard for safe email access
export function getUserEmail(user: UserInfoResponse): string | undefined {
	return user.email || user.primaryEmail || undefined;
}
