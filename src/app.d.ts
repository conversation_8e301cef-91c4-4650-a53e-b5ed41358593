// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
import type { LogtoClient, UserInfoResponse } from '@logto/sveltekit';

declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			logtoClient: LogtoClient;
			user?: UserInfoResponse;
		}
		// interface PageData {}
		// interface PageState {}
		interface Platform {
			env: {
				// Add your CloudFlare bindings here
				// KV_NAMESPACE?: KVNamespace;
				// D1_DATABASE?: D1Database;
				// R2_BUCKET?: R2Bucket;
			};
			context: {
				waitUntil(promise: Promise<any>): void;
			};
			caches: CacheStorage & { default: Cache };
		}
	}
}

export {};
