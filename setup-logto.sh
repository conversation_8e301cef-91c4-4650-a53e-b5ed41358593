#!/bin/bash

# Logto Database Setup Script
# Following official Logto documentation

echo "🚀 Setting up Logto for SourceFlex development environment..."

# Step 1: Start PostgreSQL first
echo "📦 Starting PostgreSQL..."
docker-compose -f docker-compose.dev.yml up -d postgres

# Wait for PostgreSQL to be healthy
echo "⏳ Waiting for PostgreSQL to be ready..."
sleep 10

# Step 2: Seed the Logto database
echo "🌱 Seeding Logto database..."
echo "Choose seeding method:"
echo "1. Use npx (recommended - no global installation)"
echo "2. Install Logto CLI globally"
read -p "Enter choice (1 or 2): " choice

if [ "$choice" = "1" ]; then
    echo "Using npx to seed database..."
    DB_URL="postgres://postgres:postgres@localhost:5432/logto" npx @logto/cli db seed
elif [ "$choice" = "2" ]; then
    echo "Installing Logto CLI globally..."
    npm install -g @logto/cli
    DB_URL="postgres://postgres:postgres@localhost:5432/logto" logto db seed
else
    echo "Invalid choice. Using npx method..."
    DB_URL="postgres://postgres:postgres@localhost:5432/logto" npx @logto/cli db seed
fi

# Step 3: Start remaining services
echo "🏗️ Starting Hasura and Logto services..."
docker-compose -f docker-compose.dev.yml up -d

echo "✅ Setup complete!"
echo ""
echo "🌐 Services available at:"
echo "  - PostgreSQL: localhost:5432"
echo "  - Hasura GraphQL: http://localhost:8080"
echo "  - Logto Core API: http://localhost:3001"
echo "  - Logto Admin Console: http://localhost:3002"
echo ""
echo "📝 Next steps:"
echo "  1. Visit http://localhost:3002 to set up Logto admin account"
echo "  2. Configure your applications in Logto console"
echo "  3. Test authentication flow"
