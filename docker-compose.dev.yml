services:
  # PostgreSQL Database
  postgres:
    image: postgres:17.5-alpine
    container_name: flex1-postgres
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - flex1-network

  # Hasura GraphQL Engine
  hasura:
    image: hasura/graphql-engine:latest
    container_name: flex1-hasura
    environment:
      HASURA_GRAPHQL_DATABASE_URL: ******************************************/postgres
      HASURA_GRAPHQL_ENABLE_CONSOLE: "true"
      HASURA_GRAPHQL_DEV_MODE: "true"
      HASURA_GRAPHQL_ENABLED_LOG_TYPES: startup, http-log, webhook-log, websocket-log, query-log
      HASURA_GRAPHQL_ADMIN_SECRET: admin_secret_key
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - flex1-network

  # Logto Authentication Service
  logto:
    image: ghcr.io/logto-io/logto:latest
    container_name: flex1-logto
    environment:
      DB_URL: ******************************************/logto
      TRUST_PROXY_HEADER: 1
      ENDPOINT: http://localhost:3001
      ADMIN_ENDPOINT: http://localhost:3002
      # CORS configuration
      CORS_ALLOW_ORIGIN: "http://localhost:5173"
      CORS_ALLOW_CREDENTIALS: "true"
    ports:
      - "3001:3001"  # Logto Core API
      - "3002:3002"  # Logto Admin Console
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - flex1-network

volumes:
  postgres_data:

networks:
  flex1-network:
    driver: bridge
