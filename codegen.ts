import type { CodegenConfig } from '@graphql-codegen/cli';

const config: CodegenConfig = {
  schema: {
    [process.env.HASURA_GRAPHQL_URL || 'http://localhost:8080/v1/graphql']: {
      headers: {
        'x-hasura-admin-secret': process.env.HASURA_ADMIN_SECRET || 'admin_secret_key'
      }
    }
  },
  documents: ['src/**/*.{gql,graphql}'],
  generates: {
    './src/lib/graphql/generated.ts': {
      plugins: [
        'typescript',
        'typescript-operations',
        'typed-document-node'
      ],
      config: {
        skipTypename: false,
        withHooks: false,
        withHOC: false,
        withComponent: false,
        scalars: {
          uuid: 'string',
          timestamptz: 'string',
          jsonb: 'any'
        }
      }
    }
  },
  hooks: {
    afterAllFileWrite: ['prettier --write']
  }
};

export default config;
