{"mcpServers": {"postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://postgres:postgres@localhost:5432/postgres"]}, "docker": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-docker"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop/SourceFlex"]}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"]}}}