{"mcpServers": {"postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://postgres:postgres@localhost:5432/postgres"]}, "docker": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-docker"]}, "cloudflare": {"command": "npx", "args": ["-y", "@cloudflare/mcp-server"], "env": {"CLOUDFLARE_API_TOKEN": "your-cloudflare-api-token", "CLOUDFLARE_ACCOUNT_ID": "your-cloudflare-account-id"}}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop/SourceFlex"]}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your-github-token"}}, "nhost-cloud": {"command": "npx", "args": ["-y", "@nhost/mcp-server"]}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"], "env": {"SQLITE_DB_PATH": "/Users/<USER>/Desktop/SourceFlex/Flex1/local.db"}}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your-brave-api-key"}}, "google-drive": {"command": "npx", "args": ["-y", "@google-labs/gdrive-mcp-server"], "env": {"GOOGLE_CLIENT_ID": "your-google-client-id", "GOOGLE_CLIENT_SECRET": "your-google-client-secret"}}}}