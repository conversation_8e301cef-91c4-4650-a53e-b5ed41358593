name = "flex1-dev"
main = ".svelte-kit/cloudflare/_worker.js"
compatibility_date = "2025-07-26"

[assets]
binding = "ASSETS"
directory = ".svelte-kit/cloudflare"

# Development Environment
[env.development]
name = "flex1-dev"
vars = { NODE_ENV = "development" }

# Production Environment  
[env.production]
name = "flex1-prod"
vars = { NODE_ENV = "production" }

# Optional: Staging Environment
[env.staging]
name = "flex1-staging"
vars = { NODE_ENV = "staging" }

# Database connections will be handled via Hasura GraphQL endpoint
# Add your environment-specific endpoints here:
# [env.development.vars]
# HASURA_GRAPHQL_URL = "https://dev-hasura.yourproject.com/v1/graphql"
# LOGTO_ENDPOINT = "https://dev-logto.yourproject.com"

# [env.production.vars]  
# HASURA_GRAPHQL_URL = "https://prod-hasura.yourproject.com/v1/graphql"
# LOGTO_ENDPOINT = "https://prod-logto.yourproject.com"
