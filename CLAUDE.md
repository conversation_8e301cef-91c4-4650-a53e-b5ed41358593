# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
```bash
# Start development server with all services
npm run dev
./scripts/dev-server.sh

# Simple dev server (SvelteKit only)
npm run dev:simple

# Build for production  
npm run build

# Preview production build
npm run preview
```

### Code Quality & Testing
```bash
# Type checking
npm run check
npm run check:watch

# Linting and formatting
npm run lint
npm run format

# Testing
npm run test:unit        # Vitest unit tests
npm run test:e2e         # Playwright e2e tests
npm run test            # Run all tests
```

### GraphQL Development
```bash
# Generate types from GraphQL schema
npm run codegen
npm run codegen:watch

# Database migrations
npm run migrate
npm run migrate:status
```

### Deployment
```bash
# Deploy to environments
npm run deploy:dev
npm run deploy:staging  
npm run deploy:prod

# Wrangler (CloudFlare Workers)
npm run wrangler:dev
npm run wrangler:tail:dev
npm run wrangler:tail:prod
```

## Architecture Overview

### Tech Stack
- **Frontend**: SvelteKit 2.x with TypeScript and Svelte 5 (Runes)
- **Styling**: Tailwind CSS 4.0 + shadcn-svelte UI components
- **Database**: PostgreSQL 17 with Hasura GraphQL API
- **Authentication**: Logto with role-based access control
- **Forms**: Superforms with Zod validation
- **Testing**: Vitest (unit) + Playwright (e2e)
- **Deployment**: CloudFlare Workers

### Key Architectural Patterns

#### UI Components & Styling
- **shadcn-svelte**: Primary UI component library based on bits-ui
- **Theme system**: CSS custom properties with light/dark mode support via `class` strategy
- **Design tokens**: Semantic color tokens using OKLCH color space
- **Component structure**: Import from `$lib/components/ui/[component]`
- **Utilities**: Use `cn()` utility from `$lib/utils` for conditional classes

#### Theme Configuration
```css
/* Light theme (default) */
--background: oklch(1 0 0);
--foreground: oklch(0.141 0.005 285.823);
--primary: oklch(0.623 0.214 259.815);
--card: oklch(1 0 0);
/* etc... */
```

Dark mode is handled via the `dark` class on the root element.

#### GraphQL Integration
- **URQL client** with typed operations from GraphQL Code Generator
- **Generated types**: Import from `$lib/graphql/generated.ts`
- **Schema-first**: Write `.gql` files in `src/lib/graphql/queries/` and `mutations/`
- **Deterministic codegen**: Always run `npm run codegen` after schema changes

#### Form Handling
- **Superforms + Zod**: Single source of truth for validation
- **Server-side validation**: Forms work with progressive enhancement
- **Client-side enhancement**: Real-time validation and error states

### File Structure
```
src/
├── lib/
│   ├── components/          # Reusable components
│   │   └── ui/             # shadcn-svelte UI components
│   ├── graphql/            # GraphQL operations and generated types
│   │   ├── generated.ts   # Auto-generated types
│   │   ├── queries/       # .gql query files
│   │   └── mutations/     # .gql mutation files
│   ├── schemas/            # Zod validation schemas
│   ├── utils/              # Utility functions (including cn())
│   └── hooks/              # Svelte hooks
├── routes/                  # SvelteKit routes
│   ├── (app)/              # Protected app routes
│   ├── (auth)/             # Authentication routes
│   └── api/                # API endpoints
└── styles/                  # Global styles (app.css)
```

## Development Guidelines

### Component Development
1. **Use shadcn-svelte components** from `$lib/components/ui/`
2. **Follow established patterns** from existing components
3. **Use semantic color tokens** (e.g., `bg-primary`, `text-foreground`)
4. **Maximum 200 lines per component** - Split larger components
5. **TypeScript required** for all props and events
6. **Use `cn()` utility** for conditional styling

### Component Usage Patterns
```svelte
<script lang="ts">
  import { Button } from "$lib/components/ui/button";
  import { Input } from "$lib/components/ui/input";
  import { Card, CardContent, CardHeader, CardTitle } from "$lib/components/ui/card";
  import { cn } from "$lib/utils";
</script>

<!-- Button variants -->
<Button variant="default">Primary Action</Button>
<Button variant="secondary">Secondary Action</Button>
<Button variant="outline">Outline Button</Button>

<!-- Card with proper semantic styling -->
<Card class="w-full max-w-md">
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
  </CardHeader>
  <CardContent>
    <Input placeholder="Enter text..." />
  </CardContent>
</Card>
```

### Theme and Styling
- **Use semantic tokens**: `bg-primary`, `text-muted-foreground`, `border`
- **Conditional classes**: Use `cn()` utility for dynamic styling
- **Dark mode**: Automatically handled by CSS variables and `dark:` variants
- **Custom radius**: Use `rounded-lg`, `rounded-md`, `rounded-sm` for consistent border radius

### GraphQL Workflow
1. **Write .gql files** in appropriate directories
2. **Run `npm run codegen`** to generate types
3. **Import generated types** and use with URQL
4. **Test queries** in Hasura console at `http://localhost:8080`

### Form Development
1. **Define Zod schema** in `src/lib/schemas/`
2. **Use Superforms** for server-side validation
3. **Apply shadcn-svelte form patterns** with proper styling
4. **Include proper error states** and accessibility

### Testing Strategy
- **Unit tests**: Test utility functions and complex logic
- **Component tests**: Use Vitest browser mode for Svelte components  
- **E2E tests**: Playwright for user workflows
- **Type safety**: Always run `npm run check` before commits

## Local Development Environment

### Services (via docker-compose.dev.yml)
- **SvelteKit**: http://localhost:5173
- **PostgreSQL**: localhost:5432  
- **Hasura GraphQL**: http://localhost:8080
- **Logto Auth**: http://localhost:3001
- **Logto Admin**: http://localhost:3002

### Environment Setup
Required environment variables are managed through Docker Compose. The development server script (`./scripts/dev-server.sh`) handles port management and service startup.

## Deployment Architecture

### Three-tier deployment to CloudFlare Workers:
- **Development**: `flex1-dev.workers.dev`
- **Staging**: `flex1-staging.workers.dev`  
- **Production**: `sourceflex.io`

### Deployment Process
1. **Build verification**: `npm run build`
2. **Test execution**: `npm run test`
3. **Environment deployment**: Use appropriate deploy script
4. **Log monitoring**: Use wrangler tail commands

## Key Integration Points

### Authentication Flow
- Logto handles OAuth and user management
- SvelteKit hooks manage session state
- Protected routes use server-side auth checks

### Data Flow
- Components use URQL for GraphQL operations
- Hasura provides real-time subscriptions
- Generated types ensure end-to-end type safety

### Form Processing
- Server actions handle form submission
- Client-side validation provides immediate feedback
- Progressive enhancement ensures no-JS functionality

## UI Component Guidelines

### Available Components
Based on the `src/lib/components/ui/` directory:
- **Button**: Primary UI actions
- **Input**: Form inputs with validation
- **Card**: Content containers
- **Alert**: Status messages
- **Checkbox**: Form checkboxes
- **Dropdown Menu**: Context menus and dropdowns
- **Sheet**: Slide-out panels
- **Skeleton**: Loading states
- **Tooltip**: Contextual information
- **Separator**: Visual dividers
- **Dock**: Floating action dock

### Styling Best Practices
- **Consistency**: Use established component variants
- **Accessibility**: Components include proper ARIA attributes
- **Responsiveness**: Mobile-first responsive design
- **Performance**: Lightweight components with minimal overhead