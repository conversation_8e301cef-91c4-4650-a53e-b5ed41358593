<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication Flow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>SourceFlex Authentication Flow Test</h1>
    
    <div class="test-section info">
        <h3>Test Instructions</h3>
        <p>This page tests the CORS and authentication flow between your SvelteKit app and Logto.</p>
        <ol>
            <li>Click "Test CORS" to verify CORS is working</li>
            <li>Click "Test Auth Endpoint" to test the authentication endpoint</li>
            <li>Go to <a href="http://localhost:5173/auth/login" target="_blank">http://localhost:5173/auth/login</a> to test the full flow</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>CORS Test</h3>
        <button onclick="testCORS()">Test CORS</button>
        <div id="cors-result"></div>
    </div>

    <div class="test-section">
        <h3>Authentication Endpoint Test</h3>
        <button onclick="testAuthEndpoint()">Test Auth Endpoint</button>
        <div id="auth-result"></div>
    </div>

    <div class="test-section">
        <h3>Logto Well-Known Configuration</h3>
        <button onclick="testWellKnown()">Test Well-Known Config</button>
        <div id="wellknown-result"></div>
    </div>

    <script>
        async function testCORS() {
            const resultDiv = document.getElementById('cors-result');
            resultDiv.innerHTML = '<p>Testing CORS...</p>';
            
            try {
                const response = await fetch('http://localhost:3001/oidc/.well-known/openid-configuration', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ CORS Test Successful!</h4>
                            <p>Successfully fetched Logto configuration</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ CORS Test Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testAuthEndpoint() {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.innerHTML = '<p>Testing authentication endpoint...</p>';
            
            try {
                const response = await fetch('http://localhost:5173/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'testpassword'
                    })
                });
                
                // We expect this to either redirect or return an error
                // A redirect (302) or successful response means the endpoint is working
                if (response.status === 302 || response.redirected) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Auth Endpoint Working!</h4>
                            <p>Endpoint responded with redirect (expected behavior)</p>
                            <p>Redirect URL: ${response.url || 'Not available'}</p>
                        </div>
                    `;
                } else {
                    const data = await response.json().catch(() => ({ message: 'No JSON response' }));
                    resultDiv.innerHTML = `
                        <div class="info">
                            <h4>ℹ️ Auth Endpoint Response</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Auth Endpoint Test Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testWellKnown() {
            const resultDiv = document.getElementById('wellknown-result');
            resultDiv.innerHTML = '<p>Testing Logto well-known configuration...</p>';
            
            try {
                const response = await fetch('http://localhost:3001/oidc/.well-known/openid-configuration');
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Logto Configuration Available!</h4>
                            <p>Key endpoints:</p>
                            <ul>
                                <li><strong>Authorization:</strong> ${data.authorization_endpoint}</li>
                                <li><strong>Token:</strong> ${data.token_endpoint}</li>
                                <li><strong>UserInfo:</strong> ${data.userinfo_endpoint}</li>
                            </ul>
                            <details>
                                <summary>Full Configuration</summary>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Well-Known Config Test Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>