#!/bin/bash
# Database Migration Script for Flex1
# Usage: ./scripts/migrate.sh [up|down|status]

set -e

# Configuration
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="postgres"
DB_USER="postgres"
DB_PASSWORD="postgres"
MIGRATIONS_DIR="config/migrations"

# Database connection string
DB_URL="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PostgreSQL is available
check_db_connection() {
    log_info "Checking database connection..."
    if psql "$DB_URL" -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "Database connection successful"
    else
        log_error "Cannot connect to database. Make sure PostgreSQL is running."
        log_info "Trying to connect to: $DB_URL"
        exit 1
    fi
}

# Create migrations table if it doesn't exist
create_migrations_table() {
    log_info "Creating migrations table if it doesn't exist..."
    psql "$DB_URL" -c "
        CREATE TABLE IF NOT EXISTS schema_migrations (
            id SERIAL PRIMARY KEY,
            version VARCHAR(255) UNIQUE NOT NULL,
            applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    " > /dev/null 2>&1
    log_success "Migrations table ready"
}

# Get applied migrations
get_applied_migrations() {
    psql "$DB_URL" -t -c "SELECT version FROM schema_migrations ORDER BY version;" 2>/dev/null | tr -d ' ' | grep -v '^$' || true
}

# Get available migrations
get_available_migrations() {
    find "$MIGRATIONS_DIR" -name "*.sql" -type f | sort | while read -r file; do
        basename "$file" .sql
    done
}

# Apply a migration
apply_migration() {
    local migration_file="$1"
    local migration_name=$(basename "$migration_file" .sql)
    
    log_info "Applying migration: $migration_name"
    
    # Execute the migration
    if psql "$DB_URL" -f "$migration_file" > /dev/null 2>&1; then
        # Record the migration as applied
        psql "$DB_URL" -c "INSERT INTO schema_migrations (version) VALUES ('$migration_name');" > /dev/null 2>&1
        log_success "Migration applied: $migration_name"
    else
        log_error "Failed to apply migration: $migration_name"
        exit 1
    fi
}

# Run migrations up
migrate_up() {
    check_db_connection
    create_migrations_table
    
    local applied_migrations=$(get_applied_migrations)
    local available_migrations=$(get_available_migrations)
    
    log_info "Running migrations..."
    
    local migrations_applied=0
    
    for migration in $available_migrations; do
        if echo "$applied_migrations" | grep -q "^$migration$"; then
            log_info "Skipping already applied migration: $migration"
        else
            apply_migration "$MIGRATIONS_DIR/$migration.sql"
            migrations_applied=$((migrations_applied + 1))
        fi
    done
    
    if [ $migrations_applied -eq 0 ]; then
        log_success "All migrations are up to date"
    else
        log_success "Applied $migrations_applied migration(s)"
    fi
}

# Show migration status
show_status() {
    check_db_connection
    create_migrations_table
    
    local applied_migrations=$(get_applied_migrations)
    local available_migrations=$(get_available_migrations)
    
    echo
    echo "=== MIGRATION STATUS ==="
    echo
    
    for migration in $available_migrations; do
        if echo "$applied_migrations" | grep -q "^$migration$"; then
            echo -e "${GREEN}✓${NC} $migration (applied)"
        else
            echo -e "${RED}✗${NC} $migration (pending)"
        fi
    done
    
    echo
}

# Main script logic
case "${1:-up}" in
    "up")
        migrate_up
        ;;
    "status")
        show_status
        ;;
    "down")
        log_error "Migration rollback not implemented yet"
        exit 1
        ;;
    *)
        echo "Usage: $0 [up|down|status]"
        echo "  up     - Apply pending migrations (default)"
        echo "  down   - Rollback migrations (not implemented)"
        echo "  status - Show migration status"
        exit 1
        ;;
esac
