#!/bin/bash
# Test Logto M2M Application Configuration
# This script tests the Machine-to-Machine application setup

set -e

# Load environment variables
source .env.local

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "Testing Logto M2M Application Configuration..."

# Test M2M token acquisition
log_info "Requesting M2M access token..."

response=$(curl -s -X POST \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "grant_type=client_credentials&resource=https://default.logto.app/api&scope=all" \
    -u "${LOGTO_M2M_APP_ID}:${LOGTO_M2M_APP_SECRET}" \
    "${LOGTO_ENDPOINT}/oidc/token")

# Check if we got a token
if echo "$response" | grep -q "access_token"; then
    log_success "M2M access token obtained successfully!"
    
    # Extract access token
    access_token=$(echo "$response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    
    # Test Management API access
    log_info "Testing Management API access..."
    
    api_response=$(curl -s -X GET \
        -H "Authorization: Bearer $access_token" \
        "${LOGTO_ENDPOINT}/api/organizations")
    
    if echo "$api_response" | grep -q '\[\]' || echo "$api_response" | grep -q '"id"'; then
        log_success "Management API access working!"
        log_info "Organizations endpoint response: $api_response"
    else
        log_error "Management API access failed"
        echo "Response: $api_response"
        exit 1
    fi
    
else
    log_error "Failed to obtain M2M access token"
    echo "Response: $response"
    exit 1
fi

echo
log_success "🎉 M2M Application configuration test completed successfully!"
echo
log_info "Next steps:"
echo "1. Create SvelteKit application in Logto console"
echo "2. Configure redirect URIs"
echo "3. Update environment variables"
echo
