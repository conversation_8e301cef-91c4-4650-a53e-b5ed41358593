# CloudFlare Workers Environment Setup Guide

## 🏗️ Three Environment Strategy

Your project is configured for three separate CloudFlare Worker environments:

### 1. **Development** (`flex1-dev`)
- **Purpose**: Active development and testing
- **URL**: `https://flex1-dev.your-subdomain.workers.dev`
- **Deploy**: `npm run deploy:dev`

### 2. **Staging** (`flex1-staging`)  
- **Purpose**: Pre-production testing and client demos
- **URL**: `https://flex1-staging.your-subdomain.workers.dev`
- **Deploy**: `npm run deploy:staging`

### 3. **Production** (`flex1-prod`)
- **Purpose**: Live application for users
- **URL**: `https://flex1-prod.your-subdomain.workers.dev` → `https://sourceflex.io`
- **Deploy**: `npm run deploy:prod` (with confirmation prompt)

## 🚀 Deployment Commands

```bash
# Development
npm run deploy:dev

# Staging  
npm run deploy:staging

# Production (with confirmation)
npm run deploy:prod

# Test worker locally
npm run build && npm run wrangler:dev

# Monitor logs
npm run wrangler:tail:dev    # Development logs
npm run wrangler:tail:prod   # Production logs
```

## ⚙️ Environment Variables Setup

Each environment can have its own configuration in `wrangler.toml`:

```toml
[env.development.vars]
HASURA_GRAPHQL_URL = "https://dev-hasura.yourproject.com/v1/graphql"
LOGTO_ENDPOINT = "https://dev-logto.yourproject.com"

[env.production.vars]  
HASURA_GRAPHQL_URL = "https://prod-hasura.yourproject.com/v1/graphql"
LOGTO_ENDPOINT = "https://prod-logto.yourproject.com"
```

## 🔐 Secrets Management

For sensitive data, use Wrangler secrets:

```bash
# Development secrets
npx wrangler secret put LOGTO_APP_SECRET --env development
npx wrangler secret put HASURA_ADMIN_SECRET --env development

# Production secrets  
npx wrangler secret put LOGTO_APP_SECRET --env production
npx wrangler secret put HASURA_ADMIN_SECRET --env production
```

## 🌐 Custom Domain Setup (Production)

1. **In CloudFlare Dashboard**:
   - Workers & Pages → flex1-prod → Settings → Triggers
   - Add Custom Domain: `sourceflex.io`

2. **DNS Configuration**:
   - Add CNAME record: `sourceflex.io` → `flex1-prod.your-subdomain.workers.dev`

## 🔄 Workflow

1. **Develop locally**: `npm run dev`
2. **Deploy to dev**: `npm run deploy:dev`
3. **Test and iterate**
4. **Deploy to staging**: `npm run deploy:staging`
5. **Client approval/testing**
6. **Deploy to production**: `npm run deploy:prod`

## 📁 Files Created

- `wrangler.toml` - CloudFlare configuration
- `scripts/deployment/deploy-dev.sh` - Development deployment
- `scripts/deployment/deploy-staging.sh` - Staging deployment  
- `scripts/deployment/deploy-prod.sh` - Production deployment (with confirmation)
