#!/bin/bash

# Deploy to Staging Environment
echo "🚀 Deploying to Staging Environment..."

# Build the application
echo "📦 Building application..."
npm run build

# Deploy to CloudFlare Workers (Staging)
echo "☁️ Deploying to CloudFlare Workers (Staging)..."
npx wrangler deploy --env staging

echo "✅ Staging deployment complete!"
echo "🌐 Your staging worker will be available at: https://flex1-staging.your-subdomain.workers.dev"
