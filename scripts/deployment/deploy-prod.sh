#!/bin/bash

# Deploy to Production Environment
echo "🚀 Deploying to Production Environment..."

# Confirmation prompt
read -p "⚠️ Are you sure you want to deploy to PRODUCTION? (y/N): " confirm
if [[ $confirm != [yY] ]]; then
    echo "❌ Production deployment cancelled."
    exit 1
fi

# Build the application
echo "📦 Building application..."
npm run build

# Deploy to CloudFlare Workers (Production)
echo "☁️ Deploying to CloudFlare Workers (Production)..."
npx wrangler deploy --env production

echo "✅ Production deployment complete!"
echo "🌐 Your prod worker will be available at: https://flex1-prod.your-subdomain.workers.dev"
echo "🔗 Custom domain: https://sourceflex.io (after DNS setup)"
