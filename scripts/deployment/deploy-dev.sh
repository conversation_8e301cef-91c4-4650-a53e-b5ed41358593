#!/bin/bash

# Deploy to Development Environment
echo "🚀 Deploying to Development Environment..."

# Build the application
echo "📦 Building application..."
npm run build

# Deploy to CloudFlare Workers (Development)
echo "☁️ Deploying to CloudFlare Workers (Development)..."
npx wrangler deploy --env development

echo "✅ Development deployment complete!"
echo "🌐 Your dev worker will be available at: https://flex1-dev.your-subdomain.workers.dev"
