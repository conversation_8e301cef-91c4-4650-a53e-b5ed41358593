#!/bin/bash

# Flex1 Development Server Manager
# Always runs on port 5173, kills any conflicting processes

PORT=5173
PROJECT_DIR="/Users/<USER>/Desktop/SourceFlex/Flex1"

echo "🚀 Starting Flex1 Development Server on port $PORT..."

# Function to kill processes on a specific port
kill_port() {
    local port=$1
    echo "🔍 Checking for processes on port $port..."
    
    # Find processes using the port
    local pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ ! -z "$pids" ]; then
        echo "⚠️  Found processes on port $port: $pids"
        echo "🔪 Killing processes: $pids"
        kill -9 $pids 2>/dev/null
        sleep 2
        echo "✅ Port $port cleared"
    else
        echo "✅ Port $port is free"
    fi
}

# Clear port 5173 and nearby ports (in case Vite tries alternatives)
kill_port 5173
kill_port 5174
kill_port 5175
kill_port 5176

# Wait a moment for ports to be freed
sleep 1

# Start the development server
echo "🏁 Starting SvelteKit development server..."
cd "$PROJECT_DIR"
exec vite dev --port $PORT --host 0.0.0.0
