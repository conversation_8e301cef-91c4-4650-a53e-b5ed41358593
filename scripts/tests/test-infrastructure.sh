#!/bin/bash

# Test Infrastructure Health
# Tests all Docker services and endpoints

echo "🔍 Testing Flex1 Infrastructure Health..."
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local name=$1
    local url=$2
    local expected_status=$3
    
    echo -n "Testing $name... "
    
    if response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null); then
        if [ "$response" = "$expected_status" ]; then
            echo -e "${GREEN}✅ OK (HTTP $response)${NC}"
            return 0
        else
            echo -e "${RED}❌ FAIL (HTTP $response, expected $expected_status)${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ FAIL (Connection failed)${NC}"
        return 1
    fi
}

# Test Docker containers
echo -e "\n🐳 Docker Container Status:"
docker ps --filter "name=flex1-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Test database connection
echo -e "\n💾 Database Connection:"
if docker exec flex1-postgres pg_isready -U postgres > /dev/null 2>&1; then
    echo -e "${GREEN}✅ PostgreSQL is ready${NC}"
else
    echo -e "${RED}❌ PostgreSQL connection failed${NC}"
fi

# Test endpoints
echo -e "\n🌐 Service Endpoints:"
test_endpoint "Hasura GraphQL" "http://localhost:8080/healthz" "200"
test_endpoint "Logto Core API" "http://localhost:3001/" "302"
test_endpoint "Logto Admin Console" "http://localhost:3002/" "200"

# Test GraphQL endpoint
echo -e "\n🔗 GraphQL Schema Introspection:"
if curl -s -X POST -H "Content-Type: application/json" \
    -d '{"query": "query { __schema { types { name } } }"}' \
    http://localhost:8080/v1/graphql | jq -e '.data.__schema.types' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Hasura GraphQL endpoint is working${NC}"
else
    echo -e "${RED}❌ Hasura GraphQL endpoint failed${NC}"
fi

echo -e "\n🎯 Infrastructure Status: Complete"
