#!/bin/bash
# Track authentication tables in Hasura
# This script tracks all authentication-related tables and sets up relationships

set -e

HASURA_ENDPOINT="http://localhost:8080/v1/metadata"
ADMIN_SECRET="admin_secret_key"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to execute Hasura metadata API
execute_hasura_api() {
    local query="$1"
    local description="$2"
    
    log_info "$description"
    
    response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "X-Hasura-Admin-Secret: $ADMIN_SECRET" \
        -d "$query" \
        "$HASURA_ENDPOINT")
    
    # Check for success message first
    if echo "$response" | grep -q '"message":"success"'; then
        log_success "Completed: $description"
        return 0
    fi
    
    # Check for errors but ignore "already-tracked" and "already exists" errors
    if echo "$response" | grep -q '"error":'; then
        if echo "$response" | grep -q '"already-tracked"' || echo "$response" | grep -q '"already exists"' || echo "$response" | grep -q '"already-exists"'; then
            log_info "Skipped: $description (already exists)"
            return 0
        else
            log_error "Failed: $description"
            echo "$response" | jq '.' 2>/dev/null || echo "$response"
            return 1
        fi
    else
        log_success "Completed: $description"
        return 0
    fi
}

log_info "Starting Hasura table tracking and relationship setup..."

# Track all authentication tables
execute_hasura_api '{
  "type": "pg_track_table",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "users"}
  }
}' "Tracking users table"

execute_hasura_api '{
  "type": "pg_track_table",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "organizations"}
  }
}' "Tracking organizations table"

execute_hasura_api '{
  "type": "pg_track_table",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "user_organizations"}
  }
}' "Tracking user_organizations table"

execute_hasura_api '{
  "type": "pg_track_table",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "organization_invitations"}
  }
}' "Tracking organization_invitations table"

execute_hasura_api '{
  "type": "pg_track_table",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "organization_domains"}
  }
}' "Tracking organization_domains table"

execute_hasura_api '{
  "type": "pg_track_table",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "webhook_logs"}
  }
}' "Tracking webhook_logs table"

execute_hasura_api '{
  "type": "pg_track_table",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "auth_audit_logs"}
  }
}' "Tracking auth_audit_logs table"

# Set up foreign key relationships
log_info "Setting up table relationships..."

# User Organizations -> Users
execute_hasura_api '{
  "type": "pg_create_object_relationship",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "user_organizations"},
    "name": "user",
    "using": {
      "foreign_key_constraint_on": "user_id"
    }
  }
}' "Creating user_organizations -> user relationship"

# User Organizations -> Organizations
execute_hasura_api '{
  "type": "pg_create_object_relationship",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "user_organizations"},
    "name": "organization",
    "using": {
      "foreign_key_constraint_on": "organization_id"
    }
  }
}' "Creating user_organizations -> organization relationship"

# User Organizations -> Invited By User
execute_hasura_api '{
  "type": "pg_create_object_relationship",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "user_organizations"},
    "name": "invited_by_user",
    "using": {
      "foreign_key_constraint_on": "invited_by"
    }
  }
}' "Creating user_organizations -> invited_by_user relationship"

# Users -> User Organizations (array relationship)
execute_hasura_api '{
  "type": "pg_create_array_relationship",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "users"},
    "name": "user_organizations",
    "using": {
      "foreign_key_constraint_on": {
        "table": {"schema": "public", "name": "user_organizations"},
        "column": "user_id"
      }
    }
  }
}' "Creating users -> user_organizations array relationship"

# Organizations -> User Organizations (array relationship)
execute_hasura_api '{
  "type": "pg_create_array_relationship",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "organizations"},
    "name": "user_organizations",
    "using": {
      "foreign_key_constraint_on": {
        "table": {"schema": "public", "name": "user_organizations"},
        "column": "organization_id"
      }
    }
  }
}' "Creating organizations -> user_organizations array relationship"

# Organization Invitations -> Organizations
execute_hasura_api '{
  "type": "pg_create_object_relationship",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "organization_invitations"},
    "name": "organization",
    "using": {
      "foreign_key_constraint_on": "organization_id"
    }
  }
}' "Creating organization_invitations -> organization relationship"

# Organization Invitations -> Invited By User
execute_hasura_api '{
  "type": "pg_create_object_relationship",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "organization_invitations"},
    "name": "invited_by_user",
    "using": {
      "foreign_key_constraint_on": "invited_by"
    }
  }
}' "Creating organization_invitations -> invited_by_user relationship"

# Organizations -> Organization Invitations (array relationship)
execute_hasura_api '{
  "type": "pg_create_array_relationship",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "organizations"},
    "name": "organization_invitations",
    "using": {
      "foreign_key_constraint_on": {
        "table": {"schema": "public", "name": "organization_invitations"},
        "column": "organization_id"
      }
    }
  }
}' "Creating organizations -> organization_invitations array relationship"

# Organization Domains -> Organizations
execute_hasura_api '{
  "type": "pg_create_object_relationship",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "organization_domains"},
    "name": "organization",
    "using": {
      "foreign_key_constraint_on": "organization_id"
    }
  }
}' "Creating organization_domains -> organization relationship"

# Organizations -> Organization Domains (array relationship)
execute_hasura_api '{
  "type": "pg_create_array_relationship",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "organizations"},
    "name": "organization_domains",
    "using": {
      "foreign_key_constraint_on": {
        "table": {"schema": "public", "name": "organization_domains"},
        "column": "organization_id"
      }
    }
  }
}' "Creating organizations -> organization_domains array relationship"

# Auth Audit Logs -> Users
execute_hasura_api '{
  "type": "pg_create_object_relationship",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "auth_audit_logs"},
    "name": "user",
    "using": {
      "foreign_key_constraint_on": "user_id"
    }
  }
}' "Creating auth_audit_logs -> user relationship"

# Auth Audit Logs -> Organizations
execute_hasura_api '{
  "type": "pg_create_object_relationship",
  "args": {
    "source": "default",
    "table": {"schema": "public", "name": "auth_audit_logs"},
    "name": "organization",
    "using": {
      "foreign_key_constraint_on": "organization_id"
    }
  }
}' "Creating auth_audit_logs -> organization relationship"

log_success "Hasura table tracking and relationships setup completed!"
log_info "You can now access the Hasura console at: http://localhost:8080/console"
