#!/bin/bash

# Deterministic GraphQL Code Generation for URQL
echo "🔄 Generating GraphQL types for URQL..."

# Ensure Hasura is running (for schema introspection)
if ! curl -s http://localhost:8080/v1/graphql > /dev/null; then
    echo "⚠️  Warning: Hasura not running at localhost:8080"
    echo "   Start with: ./start-dev.sh"
    echo "   Or use remote schema in codegen.ts"
fi

# Clean previous generation
echo "🧹 Cleaning previous generated files..."
rm -f src/lib/graphql/generated.ts

# Generate types
echo "⚡ Running GraphQL Code Generator..."
npm run codegen

# Verify generation succeeded
if [ -f "src/lib/graphql/generated.ts" ]; then
    echo "✅ GraphQL types generated successfully"
    echo "📊 Generated file size: $(wc -l < src/lib/graphql/generated.ts) lines"
    echo "🎯 Result: Consistent types ready for URQL"
else
    echo "❌ GraphQL generation failed"
    echo "   Check your .gql files and Hasura schema"
    exit 1
fi

echo ""
echo "🚀 Usage in your components:"
echo "   import { GetUsersDocument } from '\$lib/graphql/generated';"
echo "   const result = query({ query: GetUsersDocument });"

