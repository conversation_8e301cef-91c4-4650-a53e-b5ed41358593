#!/bin/bash
# Logto Organization Setup Script
# This script configures organization templates and roles via Logto Management API

set -e

# Configuration
LOGTO_ENDPOINT="http://localhost:3001"
LOGTO_ADMIN_ENDPOINT="http://localhost:3002" 
CLIENT_ID="your_m2m_app_id"  # You'll need to create this in Logto console
CLIENT_SECRET="your_m2m_app_secret"  # You'll need to create this in Logto console

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_info "🚀 Logto Organization Configuration Guide"
echo
log_warning "This script will guide you through configuring Logto organizations."
log_warning "Some steps require manual configuration in the Logto console."
echo

# Step 1: Check Logto connectivity
log_info "Step 1: Checking Logto connectivity..."
if curl -s "$LOGTO_ENDPOINT/oidc/.well-known/openid_configuration" > /dev/null 2>&1; then
    log_success "Logto core service is running at $LOGTO_ENDPOINT"
else
    log_error "Cannot connect to Logto core service at $LOGTO_ENDPOINT"
    exit 1
fi

if curl -s "$LOGTO_ADMIN_ENDPOINT" > /dev/null 2>&1; then
    log_success "Logto admin console is accessible at $LOGTO_ADMIN_ENDPOINT"
else
    log_error "Cannot connect to Logto admin console at $LOGTO_ADMIN_ENDPOINT"
    exit 1
fi

echo
log_info "Step 2: Manual Configuration Required"
echo
echo "Please complete the following steps in the Logto console:"
echo
echo "1. 📱 Open your browser and navigate to: $LOGTO_ADMIN_ENDPOINT"
echo
echo "2. 🏢 Navigate to 'Organizations' in the sidebar"
echo
echo "3. 📝 Create or edit the organization template with these roles:"
echo
echo "   Role: admin"
echo "   - Description: Full organization management"
echo "   - Permissions: All organization management"
echo
echo "   Role: recruiter"
echo "   - Description: Job posting and candidate management"
echo "   - Permissions: Job and candidate management"
echo
echo "   Role: benchsales"
echo "   - Description: Candidate placement and bench sales"
echo "   - Permissions: Bench candidate and placement management"
echo
echo "   Role: member"
echo "   - Description: Basic access"
echo "   - Permissions: View organization information"
echo
echo "   Role: candidate"
echo "   - Description: Apply for jobs (future implementation)"
echo "   - Permissions: Job application and profile management"
echo

read -p "Have you completed the organization template setup? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_success "Organization template configuration completed!"
else
    log_warning "Please complete the configuration and run this script again."
    exit 0
fi

echo
log_info "Step 3: Create Machine-to-Machine Application"
echo
echo "To enable webhook and API integration, create an M2M application:"
echo
echo "1. 🔧 In Logto console, go to 'Applications'"
echo "2. ➕ Click 'Create Application'"
echo "3. 🤖 Choose 'Machine-to-Machine'"
echo "4. 📝 Name it 'Flex1 Backend'"
echo "5. ✅ Grant it 'Management API' access"
echo "6. 📋 Copy the Client ID and Client Secret"
echo

read -p "Have you created the M2M application? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo
    echo "Please update your .env.local file with the M2M credentials:"
    echo
    echo "LOGTO_M2M_APP_ID=your_client_id_here"
    echo "LOGTO_M2M_APP_SECRET=your_client_secret_here"
    echo
    log_success "M2M application setup completed!"
else
    log_warning "Please complete the M2M application setup."
    exit 0
fi

echo
log_info "Step 4: Test Organization Creation"
echo
echo "You can now test creating an organization in the Logto console:"
echo
echo "1. 🏢 Go to 'Organizations' → 'Create Organization'"
echo "2. 📝 Enter a test organization name"
echo "3. ✅ Verify the roles are available"
echo "4. 🎯 Create the organization"
echo

echo
log_success "🎉 Logto organization configuration guide completed!"
echo
log_info "Next steps:"
echo "1. Update .env.local with M2M credentials"
echo "2. Test organization creation"
echo "3. Configure webhooks (next phase)"
echo
