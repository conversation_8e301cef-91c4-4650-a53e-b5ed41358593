import { test, expect } from '@playwright/test';

test.describe('Sidebar Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to a page with sidebar (assuming dashboard has sidebar)
    await page.goto('/dashboard');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display sidebar in expanded state by default', async ({ page }) => {
    // Check if sidebar is visible
    const sidebar = page.locator('[data-slot="sidebar"]');
    await expect(sidebar).toBeVisible();
    
    // Check if sidebar is in expanded state
    await expect(sidebar).toHaveAttribute('data-state', 'expanded');
    
    // Check if sidebar content is visible
    const sidebarContent = page.locator('[data-slot="sidebar-content"]');
    await expect(sidebarContent).toBeVisible();
  });

  test('should toggle sidebar when trigger button is clicked', async ({ page }) => {
    // Find the sidebar trigger button
    const triggerButton = page.locator('[data-slot="sidebar-trigger"]').or(
      page.getByRole('button', { name: /toggle sidebar/i })
    );
    
    // Initial state should be expanded
    const sidebar = page.locator('[data-slot="sidebar"]');
    await expect(sidebar).toHaveAttribute('data-state', 'expanded');
    
    // Click the trigger to collapse
    await triggerButton.click();
    
    // Wait for animation and check collapsed state
    await page.waitForTimeout(500); // Wait for animation
    await expect(sidebar).toHaveAttribute('data-state', 'collapsed');
    
    // Click again to expand
    await triggerButton.click();
    
    // Wait for animation and check expanded state
    await page.waitForTimeout(500);
    await expect(sidebar).toHaveAttribute('data-state', 'expanded');
  });

  test('should toggle sidebar with keyboard shortcut Ctrl+B', async ({ page }) => {
    const sidebar = page.locator('[data-slot="sidebar"]');
    
    // Initial state should be expanded
    await expect(sidebar).toHaveAttribute('data-state', 'expanded');
    
    // Press Ctrl+B to toggle
    await page.keyboard.press('Control+b');
    
    // Wait for animation and check collapsed state
    await page.waitForTimeout(500);
    await expect(sidebar).toHaveAttribute('data-state', 'collapsed');
    
    // Press Ctrl+B again to expand
    await page.keyboard.press('Control+b');
    
    // Wait for animation and check expanded state
    await page.waitForTimeout(500);
    await expect(sidebar).toHaveAttribute('data-state', 'expanded');
  });

  test('should toggle sidebar with keyboard shortcut Cmd+B on Mac', async ({ page }) => {
    const sidebar = page.locator('[data-slot="sidebar"]');
    
    // Initial state should be expanded
    await expect(sidebar).toHaveAttribute('data-state', 'expanded');
    
    // Press Cmd+B to toggle (Meta key)
    await page.keyboard.press('Meta+b');
    
    // Wait for animation and check collapsed state
    await page.waitForTimeout(500);
    await expect(sidebar).toHaveAttribute('data-state', 'collapsed');
  });

  test('should close sidebar with Escape key when expanded', async ({ page }) => {
    const sidebar = page.locator('[data-slot="sidebar"]');
    
    // Ensure sidebar is expanded
    await expect(sidebar).toHaveAttribute('data-state', 'expanded');
    
    // Press Escape to close
    await page.keyboard.press('Escape');
    
    // Wait for animation and check collapsed state
    await page.waitForTimeout(500);
    await expect(sidebar).toHaveAttribute('data-state', 'collapsed');
  });

  test('should persist sidebar state across page reloads', async ({ page }) => {
    const sidebar = page.locator('[data-slot="sidebar"]');
    const triggerButton = page.locator('[data-slot="sidebar-trigger"]').or(
      page.getByRole('button', { name: /toggle sidebar/i })
    );
    
    // Collapse the sidebar
    await triggerButton.click();
    await page.waitForTimeout(500);
    await expect(sidebar).toHaveAttribute('data-state', 'collapsed');
    
    // Reload the page
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Check if sidebar state is persisted
    const sidebarAfterReload = page.locator('[data-slot="sidebar"]');
    await expect(sidebarAfterReload).toHaveAttribute('data-state', 'collapsed');
  });

  test('should have proper accessibility attributes', async ({ page }) => {
    const sidebar = page.locator('[data-slot="sidebar"]');
    const triggerButton = page.locator('[data-slot="sidebar-trigger"]').or(
      page.getByRole('button', { name: /toggle sidebar/i })
    );
    
    // Check sidebar accessibility attributes
    await expect(sidebar).toHaveAttribute('role', 'complementary');
    await expect(sidebar).toHaveAttribute('aria-label', 'Navigation sidebar');
    
    // Check trigger button accessibility attributes
    await expect(triggerButton).toHaveAttribute('aria-expanded', 'true');
    await expect(triggerButton).toHaveAttribute('aria-controls', 'sidebar-content');
    
    // Collapse sidebar and check updated attributes
    await triggerButton.click();
    await page.waitForTimeout(500);
    await expect(triggerButton).toHaveAttribute('aria-expanded', 'false');
  });

  test('should handle navigation menu items correctly', async ({ page }) => {
    // Check if navigation items are present
    const navItems = page.locator('[data-slot="sidebar-content"] a');
    await expect(navItems).toHaveCount.greaterThan(0);
    
    // Check if navigation items have proper attributes
    const firstNavItem = navItems.first();
    await expect(firstNavItem).toBeVisible();
    
    // Test navigation
    const href = await firstNavItem.getAttribute('href');
    if (href && href !== '/dashboard') {
      await firstNavItem.click();
      await page.waitForLoadState('networkidle');
      
      // Verify navigation worked
      expect(page.url()).toContain(href);
    }
  });

  test('should show tooltips in collapsed state', async ({ page }) => {
    const triggerButton = page.locator('[data-slot="sidebar-trigger"]').or(
      page.getByRole('button', { name: /toggle sidebar/i })
    );
    
    // Collapse the sidebar
    await triggerButton.click();
    await page.waitForTimeout(500);
    
    // Look for menu buttons that should show tooltips
    const menuButtons = page.locator('[data-slot="sidebar-menu-button"]');
    
    if (await menuButtons.count() > 0) {
      const firstButton = menuButtons.first();
      
      // Hover over the button to trigger tooltip
      await firstButton.hover();
      
      // Wait for tooltip to appear
      await page.waitForTimeout(200);
      
      // Check if tooltip is visible (this depends on the tooltip implementation)
      const tooltip = page.locator('[role="tooltip"]');
      if (await tooltip.count() > 0) {
        await expect(tooltip).toBeVisible();
      }
    }
  });
});

test.describe('Sidebar Mobile Behavior', () => {
  test.beforeEach(async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
  });

  test('should show mobile sheet instead of desktop sidebar', async ({ page }) => {
    // On mobile, sidebar should be hidden by default
    const desktopSidebar = page.locator('[data-slot="sidebar"]:not([data-mobile="true"])');
    await expect(desktopSidebar).toBeHidden();
    
    // Mobile sidebar should be available but not visible until triggered
    const mobileSheet = page.locator('[data-mobile="true"]');
    // Mobile sheet might not be in DOM until opened
  });

  test('should open mobile sidebar when trigger is clicked', async ({ page }) => {
    const triggerButton = page.locator('[data-slot="sidebar-trigger"]').or(
      page.getByRole('button', { name: /toggle sidebar/i })
    );
    
    // Click trigger to open mobile sidebar
    await triggerButton.click();
    
    // Wait for mobile sheet to appear
    await page.waitForTimeout(500);
    
    // Check if mobile sidebar content is visible
    const mobileContent = page.locator('[data-mobile="true"]');
    if (await mobileContent.count() > 0) {
      await expect(mobileContent).toBeVisible();
    }
  });
});

test.describe('Sidebar Animations', () => {
  test('should have smooth transitions when toggling', async ({ page }) => {
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    const sidebar = page.locator('[data-slot="sidebar-container"]');
    const triggerButton = page.locator('[data-slot="sidebar-trigger"]').or(
      page.getByRole('button', { name: /toggle sidebar/i })
    );
    
    // Get initial position
    const initialBox = await sidebar.boundingBox();
    
    // Click to collapse
    await triggerButton.click();
    
    // Wait for animation to complete
    await page.waitForTimeout(400);
    
    // Get final position
    const finalBox = await sidebar.boundingBox();
    
    // Verify position changed (sidebar moved or resized)
    expect(initialBox).not.toEqual(finalBox);
  });

  test('should respect reduced motion preferences', async ({ page }) => {
    // Set reduced motion preference
    await page.emulateMedia({ reducedMotion: 'reduce' });
    
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    const sidebar = page.locator('[data-slot="sidebar-container"]');
    
    // Check if reduced motion classes are applied
    const hasReducedMotion = await sidebar.evaluate(el => 
      getComputedStyle(el).transitionDuration === '0.01ms' ||
      el.classList.contains('motion-reduce:transition-none')
    );
    
    // This test depends on CSS implementation
    // expect(hasReducedMotion).toBe(true);
  });
});

test.describe('Sidebar Performance', () => {
  test('should not cause layout shifts during toggle', async ({ page }) => {
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Measure layout stability
    const triggerButton = page.locator('[data-slot="sidebar-trigger"]').or(
      page.getByRole('button', { name: /toggle sidebar/i })
    );
    
    // Get main content position before toggle
    const mainContent = page.locator('main');
    const initialMainBox = await mainContent.boundingBox();
    
    // Toggle sidebar
    await triggerButton.click();
    await page.waitForTimeout(500);
    
    // Get main content position after toggle
    const finalMainBox = await mainContent.boundingBox();
    
    // Main content should adjust smoothly without jarring shifts
    // This is more of a visual test that would need manual verification
    expect(finalMainBox).toBeDefined();
    expect(initialMainBox).toBeDefined();
  });
});
