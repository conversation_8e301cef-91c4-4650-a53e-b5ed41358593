---
description: Repository Information Overview
alwaysApply: true
---

# Flex1 Information

## Summary
Flex1 is a full-stack SvelteKit application designed for scalable, modular development with AI assistance. The architecture prioritizes consistency, type safety, and maintainability. It features a modern tech stack with SvelteKit, TypeScript, Tailwind CSS, and Adobe Spectrum Web Components for the frontend, PostgreSQL and Hasura GraphQL for the data layer, and Logto for authentication.

## Structure
- **src/**: Main application code with SvelteKit routes, components, and libraries
- **scripts/**: Automation scripts for deployment, code generation, and development
- **config/**: Configuration files for database, Hasura, and migrations
- **e2e/**: End-to-end tests using Playwright
- **static/**: Static assets for the application
- **.svelte-kit/**: SvelteKit build output and generated files

## Language & Runtime
**Language**: TypeScript/JavaScript
**Version**: Node.js v24.2.0
**Framework**: SvelteKit 2.x with Svelte 5 (Runes)
**Build System**: Vite
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- `@logto/sveltekit`: Authentication integration
- `@urql/svelte`: GraphQL client
- `bits-ui`: UI component library
- `sveltekit-superforms`: Form enhancement
- `tailwindcss`: CSS framework (v4.0)
- `zod`: Schema validation

**Development Dependencies**:
- `@sveltejs/adapter-cloudflare`: Deployment adapter
- `@graphql-codegen/cli`: GraphQL type generation
- `@playwright/test`: E2E testing
- `vitest`: Unit testing
- `wrangler`: CloudFlare Workers CLI

## Build & Installation
```bash
# Install dependencies
npm install --legacy-peer-deps

# Start development environment
./start-dev.sh
# or
npm run dev

# Build for production
npm run build

# Generate GraphQL types
npm run codegen
```

## Docker
**Configuration**: `docker-compose.dev.yml`
**Services**:
- PostgreSQL 17 (port 5432)
- Hasura GraphQL Engine (port 8080)
- Logto Authentication (ports 3001, 3002)

**Start Command**:
```bash
./start-dev.sh
```

## Deployment
**Platform**: CloudFlare Workers
**Environments**:
- Development: `flex1-dev.workers.dev`
- Staging: `flex1-staging.workers.dev`
- Production: `flex1-prod.workers.dev` → `sourceflex.io`

**Deploy Commands**:
```bash
npm run deploy:dev
npm run deploy:staging
npm run deploy:prod
```

## Testing
**Unit Testing**: Vitest
**E2E Testing**: Playwright
**Run Commands**:
```bash
npm run test:unit
npm run test:e2e
npm run test
```

## GraphQL Development
**API Layer**: Hasura GraphQL
**Client**: URQL (`@urql/svelte` + `@urql/exchange-auth`)
**Type Generation**: GraphQL Code Generator
**Commands**:
```bash
npm run codegen
npm run codegen:watch
```