# Flex1 Authentication & Organization Setup - Development Guide

**Project:** Multi-tenant Recruitment SaaS with Logto Authentication  
**Focus:** Adobe Spectrum Web Components Dashboard Implementation  
**Last Updated:** July 28, 2025

## **MAJOR MILESTONE ACHIEVED: SPECTRUM DASHBOARD INTEGRATION COMPLETE** 🎉

### **Current Status: Phase 3.3 COMPLETED**
✅ **Adobe Spectrum Web Components Integration Fully Working**
- SSR-compatible Spectrum components with progressive enhancement
- Professional dashboard with clean stats cards and activity feeds
- Enterprise-grade design system with proper fallbacks
- Client-side loading to prevent Node.js API conflicts
- Consistent theming with Spectrum design tokens

### **Next Priority: Organization Management & User Onboarding Flow**

**New Technology Additions:**
- **Adobe Spectrum Web Components** - Enterprise design system
- **Progressive Enhancement** - SSR-safe implementation
- **Design Tokens** - Consistent theming and spacing

## **Project Overview**

Building authentication and organization management system for a multi-tenant recruitment SaaS platform that supports:
- User registration with email verification
- Organization creation and management
- Multi-tenant role-based access control
- Email domain-based auto-join functionality
- Both hiring and bench sales business models

## **Technology Stack**
- **Frontend:** SvelteKit 2.x + TypeScript + Svelte 5 Runes
- **Styling:** Tailwind CSS 4.0 + Adobe Spectrum Web Components
- **Design System:** Adobe Spectrum with SSR compatibility
- **Database:** PostgreSQL 17
- **API Layer:** Hasura GraphQL
- **Authentication:** Logto (Docker self-hosted)
- **Authorization:** Casbin RBAC
- **GraphQL Client:** URQL with auth exchange

---

## **DEVELOPMENT PHASES**

### **PHASE 1: DATABASE FOUNDATION** ✅
**Estimated Time:** 1-2 hours  
**Goal:** Set up database schema and migrations
**Status:** Completed

#### **Step 1.1: Database Migration Setup** ✅
**Status:** Completed  
**Tasks:**
- [x] Create migration directory structure
- [x] Set up migration management system
- [x] Create initial migration from schema file
- [x] Test migration on development database
- [x] Verify all tables and indexes are created

**Files Created:**
- `config/migrations/001_initial_auth_schema.sql`
- `scripts/migrate.sh`
- Updated `package.json` with migration scripts

**Validation Criteria:**
- ✅ All tables created successfully (7 tables)
- ✅ All indexes and triggers working
- ✅ Foreign key constraints properly set
- ✅ No migration errors in logs
- ✅ Schema migrations table created and first migration recorded

---

#### **Step 1.2: Hasura Metadata Configuration** ✅
**Status:** Completed  
**Tasks:**
- [x] Track all authentication tables in Hasura
- [x] Set up table relationships  
- [x] Configure computed fields if needed
- [x] Export initial metadata
- [x] Test GraphQL queries

**Files Created/Updated:**
- `config/hasura/config.yaml`
- `scripts/setup-hasura.sh` 
- Updated `package.json` with hasura:setup script

**Validation Criteria:**
- ✅ All tables visible in Hasura console
- ✅ Relationships working correctly
- ✅ Basic GraphQL queries returning data
- ✅ Complex relationship queries working

---

### **PHASE 2: LOGTO CONFIGURATION**
**Estimated Time:** 2-3 hours  
**Goal:** Configure Logto for multi-tenant authentication

#### **Step 2.1: Logto Organization Setup** ✅
**Status:** Completed  
**Tasks:**
- [x] Access Logto admin console (localhost:3002)
- [x] Create organization template with roles
- [x] Configure role permissions
- [x] Set up organization settings
- [x] Create Machine-to-Machine application
- [x] Update environment variables

**Roles Created:**
- admin: Full organization management
- recruiter: Job posting and candidate management  
- benchsales: Candidate placement and bench sales
- candidate: Apply for jobs (implementation planned for later)
- member: Basic access

**M2M Application Created:**
- Name: Flex1 Backend
- App ID: 0dqp29f0cs2cyunzqttln
- Management API access granted

**Validation Criteria:**
- ✅ Organization template created successfully
- ✅ All roles defined with proper permissions
- ✅ M2M application created and configured
- ✅ Environment variables updated

---

#### **Step 2.2: Logto Application Configuration** ✅
**Status:** Completed  
**Tasks:**
- [x] Create SvelteKit application in Logto (already existing)
- [x] Configure redirect URIs for development
- [x] Set up allowed origins
- [x] Configure JWT custom claims
- [x] Update environment variables (already done)

**Application Details:**
- Name: SourceFlex
- Type: Traditional Web App
- App ID: lhlp4v41i8o27h19yorny (already in .env.local)
- Redirect URI: http://localhost:5173/callback
- Post sign-out URI: http://localhost:5173/
- CORS origins: http://localhost:5173

**Validation Criteria:**
- ✅ SvelteKit app registered in Logto
- ✅ Redirect URIs working
- ✅ JWT tokens include organization claims
- ✅ Environment variables configured

---

#### **Step 2.3: Webhook Configuration** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Set up webhook endpoints in Logto console
- [ ] Configure webhook events (user.created, organization.created, etc.)
- [ ] Set webhook URLs pointing to SvelteKit API
- [ ] Configure webhook security (signatures)
- [ ] Test webhook delivery

**Webhook Events to Configure:**
- `PostRegister` - New user registration
- `User.Created` - Admin-created users
- `Organization.Created` - New organization
- `Organization.Membership.Updated` - Role changes

**Validation Criteria:**
- Webhooks configured in Logto
- Webhook URLs accessible from Logto container
- Test events trigger webhook calls

---

### **PHASE 3: SVELTEKIT INTEGRATION**
**Estimated Time:** 3-4 hours  
**Goal:** Integrate Logto authentication with SvelteKit

#### **Step 3.1: Logto SDK Setup** ✅
**Status:** COMPLETED (July 27, 2025)  
**Tasks:**
- [x] Install @logto/sveltekit package
- [x] Configure Logto client in hooks.server.ts
- [x] Set up authentication routes
- [x] Create authentication stores  
- [x] Test basic login/logout flow
- [x] Fix redirect URI configuration
- [x] Implement secure cookie-based session management
- [x] Create sign-up/register flow

**Files Created/Updated:**
- ✅ `src/hooks.server.ts` - Logto SDK integration
- ✅ `src/routes/auth/login/+page.server.ts` - Sign-in actions
- ✅ `src/routes/auth/login/+page.svelte` - Login UI
- ✅ `src/routes/auth/register/+page.server.ts` - Registration actions
- ✅ `src/routes/auth/register/+page.svelte` - Registration UI
- ✅ `src/routes/callback/+page.server.ts` - OAuth callback handler
- ✅ `src/routes/organization/setup/+page.server.ts` - Organization setup
- ✅ Updated `src/app.d.ts` with Logto types

**Key Implementation Details:**
- Using basic scopes: `openid`, `offline_access`, `profile`, `email`
- Callback URL: `http://localhost:5173/callback`
- Session security with secure cookies and proper expiration
- Auto-redirect logic based on user organization status
- Organization scope integration prepared for Phase 3.2

**Validation Criteria:**
- ✅ User can sign in and see authenticated state
- ✅ User can sign out and return to login page  
- ✅ Proper redirects after authentication
- ✅ Session state persists across page reloads
- ✅ Secure authentication flow working end-to-end

---

#### **Step 3.2: Webhook Integration & Database Synchronization** ✅
**Status:** COMPLETED (July 27, 2025)  
**Goal:** Complete user data synchronization between Logto and PostgreSQL database

**Tasks:**
- [x] Set up GraphQL Code Generator with proper dependencies
- [x] Create comprehensive GraphQL mutations for users and organizations  
- [x] Implement database service layer with URQL client
- [x] Build secure webhook endpoints with signature verification
- [x] Create webhook event logging system
- [x] Implement user synchronization logic (create/update)
- [x] Add comprehensive error handling and monitoring
- [x] Test complete webhook flow end-to-end

**Files Created/Updated:**
- ✅ `src/lib/graphql/database-service.ts` - Database operations service
- ✅ `src/lib/graphql/mutations/users.gql` - User GraphQL operations
- ✅ `src/lib/graphql/mutations/organizations.gql` - Organization operations  
- ✅ `src/lib/graphql/mutations/webhook-logs.gql` - Webhook logging
- ✅ `src/routes/api/webhooks/logto/+server.ts` - Main webhook endpoint
- ✅ `src/routes/api/webhooks/test/+server.ts` - Testing endpoint
- ✅ `src/routes/api/webhooks/logto-test/+server.ts` - Webhook simulation
- ✅ Updated `codegen.ts` with proper Hasura authentication
- ✅ Updated `package.json` with required dependencies

**Key Implementation Details:**
- Webhook signature verification using `logto-signature-sha-256` header
- Complete user data sync: email, username, name parsing, avatar, etc.
- Webhook event logging for monitoring and debugging
- Error handling with proper HTTP status codes
- Database upsert logic to handle both new and existing users
- UUID generation for proper database compatibility

**Webhook Events Supported:**
- ✅ `PostRegister` - New user registration sync
- ✅ `PostSignIn` - Update user last login time
- ⚪ `Organization.Created` - Ready for implementation
- ⚪ `Organization.Membership.Updated` - Ready for implementation

**Validation Criteria:**
- ✅ Webhook endpoint accepts and verifies Logto signatures
- ✅ User registration data syncs to PostgreSQL correctly
- ✅ Webhook events logged for monitoring
- ✅ Error handling prevents data corruption
- ✅ Database operations work with proper GraphQL mutations
- ✅ User data retrieval and updates function correctly

**Test Results:**
- ✅ User synchronization: Working perfectly
- ✅ Webhook logging: Complete event tracking  
- ✅ Database connectivity: All GraphQL operations successful
- ✅ Error handling: Comprehensive coverage
- ✅ Data integrity: Users properly stored with all fields
- Cookie-based redirect target storage for security
- Proper error handling for authentication failures
- Auto-redirect logic based on user authentication state

**Validation Criteria:**
- ✅ Users can log in successfully via Logto
- ✅ JWT tokens are properly handled by SvelteKit
- ✅ Authentication state persists across page refreshes
- ✅ Sign-out functionality working correctly
- ✅ Registration flow redirects to Logto properly
- ✅ Authenticated users auto-redirect to appropriate pages
- ✅ Session management secure and functional

**Technical Issues Resolved:**
- Fixed `getIdToken` undefined errors by using `locals.user` instead of manual `isAuthenticated()` calls
- Resolved redirect URI mismatch by using exact callback URLs
- Fixed resource indicator errors by simplifying initial scope requests
- Implemented proper redirect handling in SvelteKit actions
- **RESOLVED: SSR Spectrum Integration** - Fixed MutationObserver errors with client-side loading

---

#### **Step 3.3: Adobe Spectrum Web Components Integration** ✅
**Status:** COMPLETED (July 28, 2025)  
**Goal:** Implement enterprise-grade UI with Adobe Spectrum design system

**Tasks:**
- [x] Install Spectrum Web Components packages
- [x] Fix SSR compatibility issues (MutationObserver not defined)
- [x] Create SSR-safe theme wrapper with progressive enhancement
- [x] Convert dashboard to use Spectrum components
- [x] Implement client-side loading with fallbacks
- [x] Create reusable StatsCard and ActivityItem components
- [x] Add proper design token integration

**Files Created/Updated:**
- ✅ `src/lib/theme/spectrum.ts` - Client-side theme loading
- ✅ `src/lib/components/SpectrumApp.svelte` - SSR-safe theme wrapper
- ✅ `src/lib/components/spectrum/StatsCard.svelte` - Enterprise stats display
- ✅ `src/lib/components/spectrum/ActivityItem.svelte` - Activity feed component
- ✅ `src/routes/dashboard/+page.svelte` - Clean dashboard implementation
- ✅ Updated `package.json` with Spectrum dependencies

**Key Implementation Details:**
- Progressive enhancement: Works without JS, enhances with Spectrum
- Client-side only imports to prevent Node.js API conflicts
- Fallback CSS with Spectrum design tokens for SSR
- SSR-compatible wrapper components instead of direct sp-* tags
- Consistent theming with light/dark mode support

**SSR Compatibility Solution:**
- Dynamic imports with `browser` check prevent server-side execution
- Fallback theme provides consistent styling during SSR
- Components use regular HTML with Spectrum styling
- Progressive enhancement loads full Spectrum features client-side

**Validation Criteria:**
- ✅ Dashboard loads without SSR errors
- ✅ Components render consistently on server and client
- ✅ Spectrum design tokens applied correctly
- ✅ Enterprise-grade professional appearance
- ✅ Responsive design across all screen sizes
- ✅ Proper theme switching between light/dark modes

**Current User Journey:**
1. Visit app → Auto-redirect to `/auth/login` if not authenticated
2. Click "Sign In" → Redirect to Logto → Complete auth → Return to callback
3. Callback determines redirect: Dashboard (if has orgs) or Organization Setup (if new user)
4. Click "Sign Out" → Properly signed out and redirected to login

---

#### **Step 3.2: Webhook API Endpoints** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Create webhook API routes in SvelteKit
- [ ] Implement user sync webhook handler
- [ ] Implement organization sync webhook handler
- [ ] Add webhook signature verification
- [ ] Add error handling and retry logic
- [ ] Test webhook processing

**Files to Create:**
- `src/routes/api/webhooks/logto/+server.ts`
- `src/lib/server/webhooks/`
- `src/lib/server/database/`

**Validation Criteria:**
- Webhook endpoints respond correctly
- User data syncs from Logto to database
- Organization data syncs properly
- Webhook signatures verified

---

#### **Step 3.3: URQL GraphQL Client Setup** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Configure URQL client with auth exchange
- [ ] Set up Hasura JWT authentication
- [ ] Create GraphQL operations for user/org data
- [ ] Test authenticated GraphQL queries
- [ ] Set up error handling

**Files to Create/Update:**
- `src/lib/graphql/client.ts`
- `src/lib/graphql/operations/`
- `src/app.html` (URQL provider)

**Validation Criteria:**
- URQL client connects to Hasura
- JWT tokens sent with GraphQL requests
- Queries return user-specific data
- Organization-scoped data access works

---

### **PHASE 4: ORGANIZATION MANAGEMENT & USER ONBOARDING** ⏳
**Estimated Time:** 4-5 hours  
**Goal:** Complete organization management system and professional user onboarding flow
**Status:** NEXT PRIORITY

#### **Step 4.1: Organization Setup & Onboarding Flow** ⏳
**Status:** Ready to Start  
**Tasks:**
- [ ] Update callback routing to enable organization scopes
- [ ] Create professional organization setup UI
- [ ] Implement organization creation logic with Logto sync
- [ ] Add email domain verification setup
- [ ] Create role-based dashboard routing
- [ ] Implement "Join Existing Organization" flow
- [ ] Add admin invitation system

**Files to Create/Update:**
- `src/routes/organization/setup/+page.svelte` - Professional organization setup UI
- `src/routes/organization/create/+page.svelte` - New organization creation
- `src/routes/organization/join/+page.svelte` - Join existing organization
- `src/routes/dashboard/+layout.server.ts` - Role-based routing
- `src/lib/components/organization/` - Organization management components
- Update `src/hooks.server.ts` - Add organization scopes back
- Update `src/routes/callback/+page.server.ts` - Enhanced org routing logic

**Key Features to Implement:**
- Organization type selection (hiring, bench_sales, both)
- Email domain verification for auto-join
- Role assignment interface (admin, recruiter, benchsales, member)
- Invitation token generation and validation
- Organization settings management

**Validation Criteria:**
- Professional enterprise-grade UI design
- Organizations sync between Logto and PostgreSQL
- Users can create new organizations as admin
- Users can join existing organizations via email domain
- Admin users can invite team members with role assignments
- Proper dashboard routing based on user roles
- Organization webhook events properly handled

---

#### **Step 4.2: Dashboard & Navigation System** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Create role-based dashboard layouts
- [ ] Implement navigation based on organization type
- [ ] Add organization switching interface
- [ ] Create user profile management
- [ ] Add organization settings page
- [ ] Implement proper access control

**Files to Create:**
- `src/routes/dashboard/+layout.svelte` - Main dashboard layout
- `src/routes/dashboard/overview/+page.svelte` - Dashboard home
- `src/routes/dashboard/team/+page.svelte` - Team management
- `src/routes/dashboard/settings/+page.svelte` - Organization settings
- `src/lib/components/navigation/` - Navigation components
- `src/lib/stores/organization.ts` - Organization state management

**Validation Criteria:**
- Clean, professional enterprise UI design
- Role-based menu and feature access
- Organization context properly maintained
- User can switch between organizations
- Settings and profile management functional

---

### **PHASE 5: DOMAIN VERIFICATION**
**Estimated Time:** 2-3 hours  
**Goal:** Implement email domain verification and auto-join

#### **Step 5.1: Domain Verification Setup** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Create domain verification interface
- [ ] Implement DNS TXT record verification
- [ ] Add domain verification status tracking
- [ ] Create domain management UI
- [ ] Test domain verification flow

**Files to Create:**
- `src/routes/organization/domains/+page.svelte`
- `src/lib/server/domain-verification/`

**Validation Criteria:**
- Domains can be added for verification
- DNS verification works correctly
- Verification status tracked properly

---

#### **Step 5.2: Auto-Join Implementation** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Implement email domain checking during registration
- [ ] Add auto-join logic for verified domains
- [ ] Create organization selection for multiple matches
- [ ] Add auto-role assignment
- [ ] Test auto-join scenarios

**Validation Criteria:**
- Users auto-join organizations based on email domain
- Multiple domain matches handled correctly
- Auto-assigned roles work properly

---

### **PHASE 6: HASURA PERMISSIONS & RBAC**
**Estimated Time:** 3-4 hours  
**Goal:** Implement proper multi-tenant permissions

#### **Step 6.1: Hasura Row-Level Security** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Configure Hasura JWT claims
- [ ] Set up row-level permissions for users table
- [ ] Set up organization-scoped permissions
- [ ] Configure role-based access rules
- [ ] Test permission isolation

**Hasura Configuration:**
- User can only see their own profile
- Organization members can only see their organization data
- Admin can manage organization users
- Proper role-based data access

**Validation Criteria:**
- Users cannot access other organizations' data
- Role-based access working correctly
- JWT claims properly configured

---

#### **Step 6.2: Casbin Integration** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Install and configure Casbin
- [ ] Define permission policies
- [ ] Integrate with SvelteKit middleware
- [ ] Create permission checking utilities
- [ ] Test fine-grained permissions

**Files to Create:**
- `src/lib/server/casbin/`
- `config/casbin/model.conf`
- `config/casbin/policies/`

**Validation Criteria:**
- Casbin policies enforce correct permissions
- Fine-grained access control works
- Policies integrate with JWT claims

---

### **PHASE 7: UI COMPONENTS & USER EXPERIENCE**
**Estimated Time:** 4-5 hours  
**Goal:** Create polished user interface

#### **Step 7.1: Authentication UI Components** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Create login/register forms with Flowbite Svelte
- [ ] Add loading states and error handling
- [ ] Implement form validation with Zod + Superforms
- [ ] Create password reset flow
- [ ] Add social login options

**Files to Create:**
- `src/lib/components/auth/LoginForm.svelte`
- `src/lib/components/auth/RegisterForm.svelte`
- `src/lib/components/ui/`

**Validation Criteria:**
- Forms are visually appealing and functional
- Validation works correctly
- Error states handled gracefully

---

#### **Step 7.2: Organization Management UI** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Create organization dashboard
- [ ] Add member management interface
- [ ] Create role assignment UI
- [ ] Add organization settings
- [ ] Implement responsive design

**Files to Create:**
- `src/routes/dashboard/+layout.svelte`
- `src/routes/dashboard/organization/+page.svelte`
- `src/routes/dashboard/members/+page.svelte`

**Validation Criteria:**
- Dashboard is functional and responsive
- Member management works correctly
- Organization settings can be updated

---

### **PHASE 8: TESTING & VALIDATION**
**Estimated Time:** 2-3 hours  
**Goal:** Comprehensive testing of authentication system

#### **Step 8.1: Integration Testing** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Create test scenarios for user registration
- [ ] Test organization creation flows
- [ ] Test invitation and role assignment
- [ ] Test domain verification
- [ ] Test multi-tenant data isolation

**Test Scenarios:**
1. New user registers → creates organization → becomes admin
2. Admin invites users → users accept → roles assigned
3. Domain verification → auto-join functionality
4. Data isolation between organizations
5. Role-based access control

**Validation Criteria:**
- All user flows work end-to-end
- No data leakage between organizations
- Proper error handling throughout

---

#### **Step 8.2: Security Testing** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Test JWT token security
- [ ] Verify webhook signatures
- [ ] Test permission bypasses
- [ ] Check for data exposure
- [ ] Validate input sanitization

**Validation Criteria:**
- No security vulnerabilities found
- Proper authentication and authorization
- Data properly protected

---

### **PHASE 9: DEPLOYMENT PREPARATION**
**Estimated Time:** 2-3 hours  
**Goal:** Prepare for production deployment

#### **Step 9.1: Environment Configuration** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Set up production environment variables
- [ ] Configure CloudFlare Workers deployment
- [ ] Set up production database
- [ ] Configure production Logto instance
- [ ] Test production configuration

**Files to Create/Update:**
- `wrangler.toml` production environment
- `.env.production`
- Production database migration scripts

**Validation Criteria:**
- Production environment configured
- All services communicating properly
- Environment variables secured

---

#### **Step 9.2: Production Deployment** ⏳
**Status:** Not Started  
**Tasks:**
- [ ] Deploy to CloudFlare Workers
- [ ] Set up production database
- [ ] Configure production Logto
- [ ] Set up monitoring and logging
- [ ] Test production deployment

**Validation Criteria:**
- Application deployed successfully
- All features working in production
- Monitoring and logging active

---

## **DEVELOPMENT NOTES**

### **Current Infrastructure:**
- ✅ Docker setup with Postgres, Logto, Hasura
- ✅ SvelteKit project initialized
- ✅ Basic dependencies installed
- ✅ Database schema designed

### **Key Decisions Made:**
- Using Logto organizations for multi-tenancy
- PostgreSQL for primary data storage
- Hasura for GraphQL API with RLS
- URQL for GraphQL client
- Casbin for fine-grained permissions

### **Environment Details:**
- **Database:** postgres://postgres:postgres@localhost:5432/postgres
- **Hasura:** http://localhost:8080
- **Logto Core:** http://localhost:3001
- **Logto Admin:** http://localhost:3002
- **SvelteKit Dev:** http://localhost:5173

### **Next Session Checklist:**
- [ ] Review previous session's completed steps
- [ ] Check current development environment status
- [ ] Continue from the next incomplete step
- [ ] Update this documentation with progress

---

## **TROUBLESHOOTING**

### **Common Issues:**
1. **Docker Services Not Starting:** Check `docker-compose.dev.yml` and run `docker-compose up -d`
2. **Database Connection Issues:** Verify DATABASE_URL in `.env.local`
3. **Logto Console Access:** Ensure port 3002 is accessible
4. **Webhook Connectivity:** Check Docker network configuration

### **Useful Commands:**
```bash
# Start development environment
./start-dev.sh

# Stop development environment  
./stop-dev.sh

# Check Docker containers
docker ps

# View Logto logs
docker logs flex1-logto

# Access database
psql postgres://postgres:postgres@localhost:5432/postgres
```

---

**Last Updated:** July 27, 2025  
**Current Phase:** Phase 3 - SvelteKit Integration (Step 3.1 COMPLETED)  
**Next Step:** Step 3.2 - Webhook API Endpoints (for user/organization sync)

**READY FOR NEXT SESSION:**
- Basic authentication is fully functional and tested
- User can sign in, sign out, and register successfully  
- Ready to implement multi-tenant organization management
- Next priority: Webhook integration for user/org data synchronization