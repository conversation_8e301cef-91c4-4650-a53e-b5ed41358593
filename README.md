# Flex1 - SvelteKit Application

> **AI-Optimized Development Environment**  
> Built for consistent, predictable development with AI tools like Claude.

## 🎯 Project Overview

**Flex1** is a full-stack SvelteKit application designed for scalable, modular development with AI assistance. The architecture prioritizes consistency, type safety, and maintainability.

**Domain:** `sourceflex.io`  
**Local Development:** `http://localhost:5173`

---

## 🏗️ Architecture & Tech Stack

### **Frontend Framework**
- **SvelteKit 2.x** with TypeScript and Svelte 5 (Runes)
- **Tailwind CSS 4.0** for styling
- **Adobe Spectrum Web Components** - Design system (SSR-compatible)
- **CloudFlare Workers** deployment (not Pages)

### **Data & API Layer**
- **PostgreSQL 17** - Primary database
- **Hasura GraphQL** - API layer with auto-generated GraphQL
- **URQL** - Lightweight GraphQL client (`@urql/svelte` + `@urql/exchange-auth`)
- **GraphQL Code Generator** - Deterministic type generation

### **Authentication & Authorization**
- **Logto** - Modern authentication service
- **Casbin** - Role-based access control (RBAC)

### **Forms & Validation**
- **Zod** - Schema validation (single source of truth)
- **Superforms** - SvelteKit form enhancement
- **Spectrum Web Components** - Enterprise UI components

### **Services & Integrations**
- **Stripe** - Payment processing
- **Resend** - Email service (SMTP)

---

## 🚀 Quick Start

### **Prerequisites**
- **Node.js v24.2.0** (required for dependencies)
- **Docker** (for local services)

### **Installation**
```bash
# Clone the repository
git clone <repository-url>
cd Flex1

# Install dependencies
npm install --legacy-peer-deps

# Start development environment
./start-dev.sh
```

### **Development Commands**
```bash
# Start development server (with all services)
npm run dev
./scripts/dev-server.sh

# Simple dev server (no services)
npm run dev:simple

# Generate GraphQL types (deterministic)
npm run codegen
npm run codegen:watch

# Build for production
npm run build

# Type checking
npm run check
npm run check:watch

# Linting & formatting
npm run lint
npm run format

# Testing
npm run test
npm run test:e2e
```

---

## 🔧 Development Environment

### **Local Services Stack**
When running `./start-dev.sh`, these services start automatically:

- **SvelteKit Dev Server**: `http://localhost:5173`
- **PostgreSQL**: `localhost:5432`
- **Hasura GraphQL**: `http://localhost:8080`
- **Logto Auth**: `http://localhost:3001`
- **Logto Admin**: `http://localhost:3002`

### **Environment Variables**
```bash
# Database
DATABASE_URL=postgres://postgres:postgres@localhost:5432/postgres

# Hasura
HASURA_ENDPOINT=http://localhost:8080
HASURA_GRAPHQL_URL=http://localhost:8080/v1/graphql
HASURA_ADMIN_SECRET=admin_secret_key

# Logto Authentication
LOGTO_ENDPOINT=http://localhost:3001
LOGTO_APP_ID=<your-app-id>
LOGTO_APP_SECRET=<your-app-secret>
LOGTO_COOKIE_ENCRYPTION_KEY=<your-encryption-key>

# Development
NODE_ENV=development
PORT=5173
```

---

## 📁 Project Structure

```
src/
├── lib/                          # Shared libraries
│   ├── components/               # Reusable components
│   │   └── examples/            # Reference implementations
│   ├── design-system/           # AI development rules
│   │   └── AI_RULES.md         # MANDATORY patterns for AI
│   ├── graphql/                 # GraphQL operations
│   │   ├── generated.ts        # Auto-generated types
│   │   ├── queries/            # GraphQL queries (.gql files)
│   │   └── mutations/          # GraphQL mutations
│   ├── schemas/                 # Zod validation schemas
│   └── stores/                  # Svelte stores
├── modules/                      # Feature modules (future)
│   ├── auth/                    # Authentication module
│   └── dashboard/               # Dashboard module
├── routes/                       # SvelteKit routes
│   ├── +layout.server.ts        # Global server layout
│   ├── +layout.svelte          # Global client layout
│   ├── +page.server.ts         # Homepage server logic
│   └── +page.svelte            # Homepage component
└── styles/                       # Global styles

scripts/                          # Automation scripts
├── deployment/                   # Deployment scripts
│   ├── deploy-dev.sh           # Deploy to dev environment
│   ├── deploy-staging.sh       # Deploy to staging
│   └── deploy-prod.sh          # Deploy to production
└── codegen/                      # Code generation
    └── generate-types.sh        # GraphQL type generation

static/                           # Static assets
```

---

## 🎨 UI Design System

### **Adobe Spectrum Web Components**
- Enterprise-grade design system with SSR compatibility
- Accessible, consistent components
- **SSR-Safe Implementation**: Client-side loading with fallbacks
- Spectrum design tokens for consistent theming

### **Architecture Features**
- **Progressive Enhancement**: Works without JavaScript, enhances with Spectrum
- **SSR Compatibility**: Components load client-side only to prevent Node.js API conflicts
- **Theme System**: Light/dark themes with proper fallbacks
- **Design Tokens**: Consistent spacing, colors, typography

### **Mandatory AI Development Patterns**
> **📖 See `src/lib/design-system/AI_RULES.md` for complete patterns**

**Spectrum Component Pattern (ALWAYS use this structure):**
```svelte
<script lang="ts">
  import { browser } from '$app/environment';
  import StatsCard from '$lib/components/spectrum/StatsCard.svelte';
  import ActivityItem from '$lib/components/spectrum/ActivityItem.svelte';
</script>

<!-- SSR-Safe Spectrum Components -->
<div class="dashboard">
  <StatsCard 
    title="Active Jobs" 
    value="12" 
    change="+2 this week" 
    changeType="positive"
    description="Job postings currently active"
    variant="accent"
  />
  
  <ActivityItem 
    title="New application received" 
    time="2 hours ago"
    type="application"
    priority="medium"
  />
</div>
```

**Component Requirements:**
- All Spectrum components must be SSR-compatible
- Use CSS custom properties for theming
- Provide fallback styling for server-side rendering
- Follow progressive enhancement principles

---

## 🔄 GraphQL Development

### **Code Generation (Deterministic)**
```bash
# Generate types from schema
npm run codegen

# Watch mode for development
npm run codegen:watch

# Manual generation with verification
./scripts/codegen/generate-types.sh
```

### **Query Structure**
```graphql
# src/lib/graphql/queries/users.gql
query GetUsers {
  users {
    id
    email
    created_at
    updated_at
  }
}

query GetUserById($id: uuid!) {
  users_by_pk(id: $id) {
    id
    email
    created_at
    updated_at
  }
}
```

### **Usage in Components**
```typescript
import { query } from '@urql/svelte';
import { GetUsersDocument } from '$lib/graphql/generated';

const usersQuery = query({ query: GetUsersDocument });
```

---

## 🚢 Deployment

### **CloudFlare Workers (3-Tier Strategy)**

**Environments:**
- **Development**: `flex1-dev.workers.dev`
- **Staging**: `flex1-staging.workers.dev`  
- **Production**: `flex1-prod.workers.dev` → `sourceflex.io`

**Deployment Commands:**
```bash
# Deploy to development
npm run deploy:dev

# Deploy to staging
npm run deploy:staging

# Deploy to production (with confirmation)
npm run deploy:prod

# Test built worker locally
npm run wrangler:dev

# Monitor logs
npm run wrangler:tail:dev
npm run wrangler:tail:prod
```

**CloudFlare Setup:**
1. Run `npx wrangler login`
2. Set environment variables in `wrangler.toml`
3. Configure custom domain for production

---

## 🎯 AI Development Guidelines

### **For Consistent AI-Assisted Development**

#### **🚨 CRITICAL: Always Follow Design System**
1. **Read** `src/lib/design-system/AI_RULES.md` before coding
2. **Use ONLY** documented component patterns
3. **Never create** custom CSS - use flowbite-svelte + Tailwind
4. **Always import** components consistently

#### **Component Development Rules**
1. **Always use Spectrum design tokens** - CSS custom properties for consistency
2. **SSR-Safe implementations** - Client-side loading with fallbacks
3. **Progressive enhancement** - Works without JS, enhances with Spectrum
4. **Follow Spectrum patterns** - Use existing StatsCard, ActivityItem components
5. **Avoid direct sp-* tags** - Use wrapper components for SSR compatibility

#### **GraphQL Development Rules**
1. **Run codegen** after schema changes - `npm run codegen`
2. **Use generated types** - import from `$lib/graphql/generated`
3. **Write .gql files** in appropriate folders (queries/mutations)
4. **Test with URQL** - lightweight, predictable client

#### **Module Development Rules**
1. **Maximum 300 lines per file** (target: 200 lines)
2. **Split files proactively** when approaching limits
3. **Use Desktop Commander** for file operations
4. **Feature-based modules** - auth/, billing/, dashboard/
5. **No cross-module dependencies** without explicit interfaces

---

## 🧪 Testing

### **Unit Testing**
```bash
npm run test:unit
```

### **End-to-End Testing**
```bash
npm run test:e2e
```

### **Type Checking**
```bash
npm run check
```

---

## 📚 Documentation

### **Primary References**
- **Adobe Spectrum**: https://spectrum.adobe.com/
- **Spectrum Web Components**: https://opensource.adobe.com/spectrum-web-components/
- **Superforms**: https://superforms.rocks/
- **SvelteKit**: https://svelte.dev/docs/kit
- **Hasura**: https://hasura.io/docs/
- **Logto**: https://docs.logto.io/
- **URQL**: https://commerce.nearform.com/open-source/urql/
- **Zod**: https://zod.dev/

### **Internal Documentation**
- **AI Rules**: `src/lib/design-system/AI_RULES.md`
- **Form Examples**: `src/lib/components/examples/`
- **Deployment Guide**: `scripts/deployment/README.md`

---

## 🔧 Troubleshooting

### **Common Issues**

**Node.js Version Error:**
```bash
# Use --legacy-peer-deps for Node.js v24.x
npm install --legacy-peer-deps
```

**Hasura Not Running:**
```bash
# Restart development environment
./stop-dev.sh
./start-dev.sh
```

**GraphQL Types Out of Date:**
```bash
# Regenerate types
npm run codegen
```

**Spectrum SSR Issues:**
```bash
# MutationObserver not defined (SSR issue)
# Fixed with client-side loading and fallbacks
```

**Component Development:**
```bash
# Test Spectrum components
npm run dev
# Visit http://localhost:5173/dashboard
```

---

## 👥 Development Team Guidelines

### **AI Tool Integration**
- **Primary AI**: Claude for development assistance
- **Follow patterns**: Always reference AI_RULES.md
- **Consistent outputs**: Use deterministic tools (codegen, scripts)
- **Code review**: Ensure AI-generated code follows project patterns

### **Collaboration**
- **Modular development**: Work in isolated feature modules
- **Shared components**: Use `src/lib/components/` for reusable parts
- **Type safety**: Always use TypeScript and generated types
- **Documentation**: Update README when adding new patterns

---

## 📄 License

**Private Project** - All rights reserved.

---

**🎯 Built for AI-Assisted Development Excellence**

This project structure ensures consistent, predictable development patterns that work seamlessly with AI tools while maintaining professional-grade architecture and scalability.
